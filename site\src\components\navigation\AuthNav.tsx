"use client"

import React, { useState } from "react";
import { authClient } from "@/lib/auth-client";
import { Button } from "@/components/ui/button";
import { ModernButton } from "@/components/ui/modern-button";
import {
  Menu,
  X,
  User,
  LogOut,
  Settings,
  Home,
  Package,
  FolderOpen,
  Tags,
  Truck,
  Search
} from "lucide-react";
import { navigate } from "astro:transitions/client";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
  DrawerFooter,
} from "@/components/ui/drawer";

export function AuthNav() {
  const { data: session, isPending } = authClient.useSession();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navigationItems = [
    { href: "/", label: "Главная", icon: Home },
    { href: "/catalog", label: "Каталог", icon: Package },
    { href: "/categories", label: "Категории", icon: FolderOpen },
    { href: "/brands", label: "Бренды", icon: Tags },
    { href: "/equipment", label: "Техника", icon: Truck },
    { href: "/search", label: "Поиск", icon: Search },
  ];

  const handleNavigation = (href: string) => {
    setIsMenuOpen(false);
    navigate(href);
  };

  if (isPending) return null;

  if (!session) {
    return (
      <div className="flex items-center gap-2">
        {/* Десктопное меню */}
        <nav className="hidden md:flex items-center gap-4 text-sm lg:gap-6">
          {navigationItems.map((item) => (
            <a
              key={item.href}
              href={item.href}
              className="transition-colors hover:text-foreground/80 text-foreground/60 flex items-center gap-2"
              onClick={(e) => {
                e.preventDefault();
                handleNavigation(item.href);
              }}
            >
              <item.icon className="h-4 w-4" />
              <span className="hidden lg:inline">{item.label}</span>
            </a>
          ))}
        </nav>

        {/* Кнопки входа для десктопа */}
        <div className="hidden md:flex items-center gap-2">
          <a href="/login" className="text-sm text-foreground/80 hover:underline">Вход</a>
          <a href="/register" className="text-sm text-foreground/80 hover:underline">Регистрация</a>
        </div>

        {/* Мобильное меню */}
        <Drawer open={isMenuOpen} onOpenChange={setIsMenuOpen}>
          <DrawerTrigger asChild>
            <ModernButton
              variant="ghost"
              size="icon"
              className="md:hidden"
            >
              <Menu className="h-5 w-5" />
            </ModernButton>
          </DrawerTrigger>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>Меню</DrawerTitle>
            </DrawerHeader>
            <nav className="px-4 space-y-2">
              {navigationItems.map((item) => (
                <a
                  key={item.href}
                  href={item.href}
                  className="flex items-center gap-3 px-3 py-3 rounded-lg transition-colors hover:bg-accent hover:text-accent-foreground"
                  onClick={(e) => {
                    e.preventDefault();
                    handleNavigation(item.href);
                  }}
                >
                  <item.icon className="h-5 w-5" />
                  <span className="font-medium">{item.label}</span>
                </a>
              ))}
            </nav>
            <DrawerFooter>
              <Button
                variant="outline"
                className="w-full"
                onClick={(e) => {
                  e.preventDefault();
                  handleNavigation("/login");
                }}
              >
                Войти
              </Button>
              <Button
                variant="default"
                className="w-full"
                onClick={(e) => {
                  e.preventDefault();
                  handleNavigation("/register");
                }}
              >
                Регистрация
              </Button>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {/* Десктопное меню */}
      <nav className="hidden md:flex items-center gap-4 text-sm lg:gap-6">
        {navigationItems.map((item) => (
          <a
            key={item.href}
            href={item.href}
            className="transition-colors hover:text-foreground/80 text-foreground/60 flex items-center gap-2"
            onClick={(e) => {
              e.preventDefault();
              handleNavigation(item.href);
            }}
          >
            <item.icon className="h-4 w-4" />
            <span className="hidden lg:inline">{item.label}</span>
          </a>
        ))}
      </nav>

      {/* Пользовательское меню для десктопа */}
      <div className="hidden md:flex items-center gap-2">
        <a href="/account" className="text-sm text-foreground/80 hover:underline">Кабинет</a>
        <Button
          size="sm"
          variant="outline"
          onClick={async () => {
            await authClient.signOut({
              fetchOptions: {
                onSuccess: () => {
                  window.location.href = "/";
                }
              },
            });
          }}
        >
          Выйти
        </Button>
      </div>

      {/* Мобильное меню */}
      <Drawer open={isMenuOpen} onOpenChange={setIsMenuOpen}>
        <DrawerTrigger asChild>
          <ModernButton
            variant="ghost"
            size="icon"
            className="md:hidden"
          >
            <Menu className="h-5 w-5" />
          </ModernButton>
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <div className="flex items-center gap-2">
              <User className="h-5 w-5" />
              <DrawerTitle>{session.user?.name || "Пользователь"}</DrawerTitle>
            </div>
          </DrawerHeader>
          <nav className="px-4 space-y-2">
            {navigationItems.map((item) => (
              <a
                key={item.href}
                href={item.href}
                className="flex items-center gap-3 px-3 py-3 rounded-lg transition-colors hover:bg-accent hover:text-accent-foreground"
                onClick={(e) => {
                  e.preventDefault();
                  handleNavigation(item.href);
                }}
              >
                <item.icon className="h-5 w-5" />
                <span className="font-medium">{item.label}</span>
              </a>
            ))}
          </nav>
          <DrawerFooter>
            <ModernButton
              variant="ghost"
              className="w-full justify-start gap-3"
              onClick={(e) => {
                e.preventDefault();
                handleNavigation("/account");
              }}
            >
              <User className="h-4 w-4" />
              Кабинет
            </ModernButton>
            <ModernButton
              variant="ghost"
              className="w-full justify-start gap-3"
              onClick={(e) => {
                e.preventDefault();
                handleNavigation("/settings");
              }}
            >
              <Settings className="h-4 w-4" />
              Настройки
            </ModernButton>
            <ModernButton
              variant="ghost"
              className="w-full justify-start gap-3 text-destructive hover:text-destructive"
              onClick={async () => {
                await authClient.signOut({
                  fetchOptions: {
                    onSuccess: () => {
                      window.location.href = "/";
                    }
                  },
                });
              }}
            >
              <LogOut className="h-4 w-4" />
              Выйти
            </ModernButton>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </div>
  );
}

