/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SortOrderSchema } from '../enums/SortOrder.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigMinOrderByAggregateInput>;
export const CategoryPageConfigMinOrderByAggregateInputObjectSchema: SchemaType = z.object({
    id: z.lazy(() => SortOrderSchema).optional().optional(), categoryId: z.lazy(() => SortOrderSchema).optional().optional(), listTemplateSlug: z.lazy(() => SortOrderSchema).optional().optional(), detailTemplateSlug: z.lazy(() => SortOrderSchema).optional().optional(), enabled: z.lazy(() => SortOrderSchema).optional().optional(), createdAt: z.lazy(() => SortOrderSchema).optional().optional(), updatedAt: z.lazy(() => SortOrderSchema).optional().optional()
}).strict() as SchemaType;
