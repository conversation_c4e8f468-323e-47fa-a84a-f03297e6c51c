/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PageTemplateCountAggregateInputType>;
export const PageTemplateCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), area: z.literal(true).optional().optional(), slug: z.literal(true).optional().optional(), title: z.literal(true).optional().optional(), description: z.literal(true).optional().optional(), defaultConfig: z.literal(true).optional().optional(), enabled: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
