/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { TemplateAreaSchema } from '../enums/TemplateArea.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.NestedEnumTemplateAreaFilter>;
export const NestedEnumTemplateAreaFilterObjectSchema: SchemaType = z.object({
    equals: z.lazy(() => TemplateAreaSchema).optional().optional(), in: z.lazy(() => TemplateAreaSchema).array().optional().optional(), notIn: z.lazy(() => TemplateAreaSchema).array().optional().optional(), not: z.union([z.lazy(() => TemplateAreaSchema),
    z.lazy(() => NestedEnumTemplateAreaFilterObjectSchema)]).optional()
}).strict() as SchemaType;
