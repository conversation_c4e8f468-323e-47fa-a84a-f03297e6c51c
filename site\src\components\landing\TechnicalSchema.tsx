"use client"

import { useState } from "react"
import { motion } from "motion/react"
import { Badge } from "@/components/ui/badge"
import { Database, Info, Search, Target, TrendingUp, CheckCircle2 } from "lucide-react"

export function TechnicalSchema() {
  const [activeConnection, setActiveConnection] = useState<number | null>(null)
  const [searchQuery, setSearchQuery] = useState("")

  const parts = [
    {
      id: 1,
      original: "Corteco 12345-ABC",
      specs: "25×47×7mm, NBR, -40°C/+120°C",
      alternatives: ["SKF 789-XYZ", "Febi 456-DEF", "NOK 321-GHI"],
      category: "Сальники",
      compatibility: 98,
      savings: 35,
    },
    {
      id: 2,
      original: "John Deere RE12345",
      specs: "Передаточное число 1:4.5, крутящий момент 850 Нм",
      alternatives: ["Komatsu 708-1W-00151", "Caterpillar 123-4567"],
      category: "Редукторы",
      compatibility: 95,
      savings: 45,
    },
  ] as const

  return (
    <div className="relative">
      <div className="absolute inset-0 bg-primary/5 rounded-2xl" />
      <div className="relative bg-card backdrop-blur-xl border rounded-2xl p-6 shadow-2xl">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Search Interface */}
          <div className="space-y-3">
            <div className="bg-secondary backdrop-blur-xl border rounded-lg">
              <div className="p-6 border-b">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-background rounded-lg flex items-center justify-center">
                    <Search className="w-5 h-5 text-foreground" />
                  </div>
                  <h3 className="text-2xl font-semibold text-card-foreground">Поиск взаимозаменяемых деталей</h3>
                </div>
                <div className="relative group">
                  <div className="absolute -inset-0.5 bg-primary rounded-lg blur opacity-30 group-hover:opacity-60 transition duration-300" />
                  <div className="relative">
                    <Search className="absolute left-4 top-4 w-5 h-5 text-muted-foreground" />
                    <input
                      type="text"
                      placeholder="Введите артикул или OEM номер..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-12 pr-4 py-4 bg-background/50 backdrop-blur-xl border rounded-lg text-foreground placeholder:text-muted-foreground focus:border-primary/50 focus:outline-none focus:ring-2 focus:ring-ring/20 transition-all duration-300"
                    />
                  </div>
                </div>
              </div>
              <div className="p-6 space-y-3">
                {parts.map((part) => (
                  <div
                    key={part.id}
                    className={`p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                      activeConnection === part.id
                        ? "bg-primary/20 border-primary/50"
                        : "bg-background backdrop-blur-xl border hover:border-border-strong"
                    }`}
                    onClick={() => setActiveConnection(activeConnection === part.id ? null : part.id)}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-mono text-base text-foreground font-medium">{part.original}</span>
                      <Badge className="bg-secondary text-secondary-foreground text-sm border-0">{part.category}</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground mb-3">{part.specs}</div>
                    <div className="flex items-center gap-4 text-sm">
                      <span className="text-chart-2 flex items-center gap-1">
                        <div className="w-2 h-2 bg-chart-2 rounded-full animate-pulse" />
                        Совместимость: {part.compatibility}%
                      </span>
                      <span className="text-primary flex items-center gap-1">
                        <TrendingUp className="w-3 h-3" />
                        Экономия: до {part.savings}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Results Panel */}
          <div className="space-y-3">
            <div className="bg-secondary backdrop-blur-xl border rounded-lg">
              <div className="p-6 border-b">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-background rounded-lg flex items-center justify-center">
                    <Database className="w-5 h-5 text-foreground" />
                  </div>
                  <h3 className="text-2xl font-semibold text-card-foreground">Результаты анализа</h3>
                </div>
              </div>

              <div className="p-6">
                {activeConnection ? (
                  <div className="space-y-4">
                    {(() => {
                      const part = parts.find((p) => p.id === activeConnection)
                      if (!part) return null
                      return (
                        <div className="space-y-4">
                          <div className="bg-background backdrop-blur-xl rounded-xl p-4 border">
                            <div className="text-base font-medium text-foreground mb-2 flex items-center gap-2">
                              <Target className="w-4 h-4 text-primary" />
                              Оригинальная деталь:
                            </div>
                            <div className="font-mono text-primary text-2xl">{part.original}</div>
                            <div className="text-sm text-muted-foreground mt-2">{part.specs}</div>
                          </div>
                          <div className="space-y-3">
                            <div className="text-base font-medium text-foreground flex items-center gap-2">
                              <CheckCircle2 className="w-4 h-4 text-chart-2" />
                              Найденные аналоги:
                            </div>
                            {part.alternatives.map((alt, index) => (
                              <motion.div
                                key={index}
                                initial={{ opacity: 0, x: -20 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                viewport={{ once: true }}
                                className="flex items-center gap-3 p-3 bg-background rounded-xl border backdrop-blur-xl"
                              >
                                <CheckCircle2 className="w-4 h-4 text-chart-2 flex-shrink-0" />
                                <div className="flex-1">
                                  <div className="font-mono text-base text-foreground">{alt}</div>
                                  <div className="text-sm text-muted-foreground">Физическая совместимость подтверждена</div>
                                </div>
                                <div className="text-sm text-chart-2 font-medium">-{part.savings}%</div>
                              </motion.div>
                            ))}
                          </div>
                          <div className="bg-background border rounded-xl p-4 backdrop-blur-xl">
                            <div className="flex items-center gap-2 text-primary text-base font-medium mb-2">
                              <Info className="w-4 h-4" />
                              Техническое заключение
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Все найденные аналоги имеют идентичные технические характеристики и могут использоваться
                              как прямая замена без модификаций.
                            </div>
                          </div>
                        </div>
                      )
                    })()}
                  </div>
                ) : (
                  <div className="py-12 text-center">
                    <div className="w-16 h-16 bg-background rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Database className="w-8 h-8 text-foreground" />
                    </div>
                    <div className="text-muted-foreground">Выберите деталь для анализа совместимости</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

