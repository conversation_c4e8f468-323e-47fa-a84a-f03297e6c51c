/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryCreateWithoutPageConfigInputObjectSchema } from './PartCategoryCreateWithoutPageConfigInput.schema';
import { PartCategoryUncheckedCreateWithoutPageConfigInputObjectSchema } from './PartCategoryUncheckedCreateWithoutPageConfigInput.schema';
import { PartCategoryCreateOrConnectWithoutPageConfigInputObjectSchema } from './PartCategoryCreateOrConnectWithoutPageConfigInput.schema';
import { PartCategoryUpsertWithoutPageConfigInputObjectSchema } from './PartCategoryUpsertWithoutPageConfigInput.schema';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryUpdateToOneWithWhereWithoutPageConfigInputObjectSchema } from './PartCategoryUpdateToOneWithWhereWithoutPageConfigInput.schema';
import { PartCategoryUpdateWithoutPageConfigInputObjectSchema } from './PartCategoryUpdateWithoutPageConfigInput.schema';
import { PartCategoryUncheckedUpdateWithoutPageConfigInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutPageConfigInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpdateOneRequiredWithoutPageConfigNestedInput>;
export const PartCategoryUpdateOneRequiredWithoutPageConfigNestedInputObjectSchema: SchemaType = z.object({
    create: z.union([z.lazy(() => PartCategoryCreateWithoutPageConfigInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutPageConfigInputObjectSchema)]).optional(), connectOrCreate: z.lazy(() => PartCategoryCreateOrConnectWithoutPageConfigInputObjectSchema).optional().optional(), upsert: z.lazy(() => PartCategoryUpsertWithoutPageConfigInputObjectSchema).optional().optional(), connect: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema).optional().optional(), update: z.union([z.lazy(() => PartCategoryUpdateToOneWithWhereWithoutPageConfigInputObjectSchema), z.lazy(() => PartCategoryUpdateWithoutPageConfigInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutPageConfigInputObjectSchema)]).optional()
}).strict() as SchemaType;
