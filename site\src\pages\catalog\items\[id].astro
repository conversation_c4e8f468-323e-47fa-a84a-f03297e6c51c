---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/clients";
import CatalogItemIsland from "@/components/catalog/islands/CatalogItemIsland";
import { renderTemplateString } from "@/lib/catalog";

const { id } = Astro.params;
if (!id) return Astro.redirect('/catalog');

const itemId = Number(id);
if (isNaN(itemId)) return Astro.redirect('/catalog');

let item: any = null; // Загрузим полные данные
let metaTitle: string = '';
let h1: string = '';
let metaDescription: string = '';
try {
  item = await trpcClient.crud.catalogItem.findUnique.query({
    where: { id: itemId },
    include: {
      brand: true,
      attributes: {
        include: {
          template: true,
        },
      },
      applicabilities: {
        include: {
          part: {
            include: {
              partCategory: true
            }
          }
        }
      },
      image: true,
      mediaAssets: true
    }
  });
} catch (error) {
  console.error("Failed to load CatalogItem:", error);
}

if (!item) {
  // Можно показать страницу 404
  return Astro.redirect('/catalog');
}
// Попробуем определить категорию (по первой применимости)
const category = item.applicabilities?.[0]?.part?.partCategory ?? null;
let pageConfig: any = null;
if (category?.id) {
  try {
    pageConfig = await trpcClient.crud.categoryPageConfig.findUnique.query({ where: { categoryId: category.id } });
  } catch {}
}

// Собираем простые переменные для шаблонов
const vars: Record<string, string> = {
  brand: item?.brand?.name ?? '',
  sku: item?.sku ?? '',
  category: category?.name ?? '',
  title: item?.applicabilities?.[0]?.part?.name ?? '',
};
const detailTexts = pageConfig?.textTemplates?.detail ?? {};
h1 = renderTemplateString(detailTexts.h1 ?? (vars.title || vars.sku || 'Товар'), vars);
metaTitle = renderTemplateString(detailTexts.title ?? (vars.title || `Артикул ${vars.sku}`), vars);
metaDescription = renderTemplateString(detailTexts.metaDescription ?? `Каталожная позиция от ${vars.brand}`, vars);
---

<MainLayout title={metaTitle || (item?.sku ? `Артикул ${item.sku}` : 'Товар')} description={metaDescription || `Каталожная позиция от ${item?.brand?.name}`}>
  <h1 class="text-3xl font-bold tracking-tight mb-4">{h1 || (item?.sku ? `Артикул ${item.sku}` : 'Товар')}</h1>
  <CatalogItemIsland client:load item={item} />
</MainLayout>


