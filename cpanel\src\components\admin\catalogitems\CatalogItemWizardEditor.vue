<template>
  <div class="catalog-item-wizard-editor">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
        Каталожные позиции
      </h3>
      <VButton @click="addCatalogItem" outlined size="small">
        Добавить
        <PlusIcon />
      </VButton>
    </div>

    <div v-if="modelValue.length === 0" class="text-center py-10">
      <div class="text-surface-600 dark:text-surface-400 mb-4">
        Вы можете добавить каталожные позиции сейчас или пропустить этот шаг.
      </div>
      <VButton size="small" @click="addCatalogItem" outlined>
        Добавить
        <PlusIcon />
      </VButton>
    </div>

    <div v-else class="space-y-4">
      <VCard
        v-for="(item, index) in modelValue"
        :key="index"
        class="border border-surface-200 dark:border-surface-700"
      >
        <template #content>
          <div class="p-4">
            <!-- Заголовок карточки -->
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center gap-3">
                <VTag
                  :value="`Позиция ${index + 1}`"
                  severity="secondary"
                  size="small"
                />
                <VTag
                  v-if="item.isExisting"
                  value="Существующая"
                  severity="info"
                  size="small"
                />
                <VTag v-else value="Новая" severity="success" size="small" />
              </div>

              <!-- Кнопки действий -->
              <div class="flex gap-2">
                <!-- В режиме редактирования для существующих позиций -->
                <template
                  v-if="
                    mode === 'edit' && item.isExisting && item.applicabilityId
                  "
                >
                  <DangerButton
                    @click="unlinkCatalogItem(item, index)"
                    severity="secondary"
                    outlined
                    size="small"
                    v-tooltip="'Отвязать от запчасти'"
                  >
                    Отвязать <UnlinkIcon class="w-4 h-4" />
                  </DangerButton>
                  <DangerButton
                    @click="deleteCatalogItem(item, index)"
                    severity="danger"
                    outlined
                    size="small"
                    v-tooltip="'Удалить каталожную позицию'"
                  >
                    Удалить <Trash2Icon class="w-4 h-4" />
                  </DangerButton>
                </template>

                <!-- Обычная кнопка удаления из списка -->
                <VButton
                  @click="removeCatalogItem(index)"
                  severity="danger"
                  size="small"
                  text
                  v-tooltip="
                    mode === 'edit' && item.isExisting
                      ? 'Убрать из списка'
                      : 'Удалить'
                  "
                >
                  <Icon name="pi pi-times" class="w-5 h-5" />
                </VButton>
              </div>
            </div>

            <!-- Переключатель типа позиции -->
            <div class="mb-4">
              <VSelectButton
                v-model="item.isExisting"
                :options="itemTypeOptions"
                optionLabel="label"
                optionValue="value"
                class="w-full md:w-auto"
              />
            </div>

            <!-- Поиск существующей позиции -->
            <div v-if="item.isExisting" class="mb-4">
              <label
                class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
              >
                Поиск каталожной позиции *
              </label>
              <VAutoComplete
                :model-value="getDisplayLabel(item.existingCatalogItem)"
                @update:model-value="onItemSelect(index, $event)"
                :suggestions="catalogItemSuggestions"
                @complete="searchCatalogItems"
                field="displayLabel"
                placeholder="Поиск по артикулу или бренду..."
                class="w-full"
                dropdown
              >
                <template #option="{ option }">
                  <div class="flex items-center gap-2">
                    <span class="font-mono font-medium">{{ option.sku }}</span>
                    <VTag
                      :value="option.brand?.name"
                      severity="secondary"
                      size="small"
                    />
                  </div>
                </template>
              </VAutoComplete>
            </div>

            <!-- Создание новой позиции -->
            <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Артикул (SKU) *
                </label>
                <VInputText
                  v-model="item.sku"
                  placeholder="Например: 12345-ABC"
                  class="w-full"
                />
              </div>

              <div>
                <label
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Бренд *
                </label>
                <div class="flex gap-2">
                  <VAutoComplete
                    v-model="item.selectedBrand"
                    :suggestions="brandSuggestions"
                    @complete="searchBrands"
                    option-label="name"
                    :force-selection="true"
                    placeholder="Поиск бренда..."
                    class="flex-1"
                    dropdown
                  />
                  <VButton
                    @click="showCreateBrand = true"
                    severity="secondary"
                    outlined
                    size="small"
                    v-tooltip="'Создать новый бренд'"
                  >
                    <Icon name="pi pi-plus" class="w-5 h-5" />
                  </VButton>
                </div>
              </div>

              <div class="md:col-span-2">
                <label
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Описание
                </label>
                <VInputText
                  v-model="item.description"
                  placeholder="Описание каталожной позиции..."
                  class="w-full"
                />
              </div>
            </div>

            <!-- Атрибуты (только для новых позиций) -->
            <div v-if="!item.isExisting" class="mb-4">
              <div class="flex items-center justify-between mb-3">
                <h4
                  class="text-md font-medium text-surface-900 dark:text-surface-0"
                >
                  Атрибуты каталожной позиции
                </h4>
                <VButton
                  @click="openAttributeManager(index)"
                  severity="secondary"
                  outlined
                  size="small"
                  label="Добавить атрибут"
                >
                  <template #icon>
                    <Icon name="pi pi-plus" class="w-5 h-5" />
                  </template>
                </VButton>
              </div>

              <!-- Список атрибутов -->
              <div
                v-if="item.attributes && item.attributes.length > 0"
                class="space-y-2"
              >
                <div
                  v-for="(attribute, attrIndex) in item.attributes"
                  :key="attrIndex"
                  class="flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"
                >
                  <div class="flex-1">
                    <div
                      class="font-medium text-surface-900 dark:text-surface-0 text-sm"
                    >
                      {{ attribute.template?.title || attribute.templateTitle }}
                      <span
                        v-if="attribute.template?.isRequired"
                        class="text-red-500 ml-1"
                        >*</span
                      >
                    </div>
                    <div class="text-sm text-surface-600 dark:text-surface-400">
                      {{ attribute.value }}
                      {{
                        attribute.template?.unit
                          ? getUnitLabel(attribute.template.unit)
                          : ""
                      }}
                      <span
                        v-if="attribute.template?.group?.name"
                        class="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"
                      >
                        {{ attribute.template.group.name }}
                      </span>
                    </div>
                  </div>
                  <VButton
                    @click="removeAttribute(index, attrIndex)"
                    severity="danger"
                    size="small"
                    text
                  >
                    <Icon name="pi pi-trash" class="w-5 h-5" />
                  </VButton>
                </div>
              </div>

              <div
                v-else
                class="text-center py-4 text-surface-500 text-sm border border-dashed rounded"
              >
                Атрибуты не добавлены
              </div>
            </div>

            <!-- Точность применимости -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Точность применимости *
                </label>
                <VSelect
                  v-model="item.accuracy"
                  :options="accuracyOptions"
                  option-label="label"
                  option-value="value"
                  placeholder="Выберите точность"
                  class="w-full"
                />
              </div>

              <div>
                <label
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Примечания
                </label>
                <VTextarea
                  v-model="item.notes"
                  :auto-resize="true"
                  rows="2"
                  placeholder="Дополнительные примечания..."
                  class="w-full"
                />
              </div>
            </div>
          </div>
        </template>
      </VCard>
    </div>

    <!-- Диалог создания бренда -->
    <QuickCreateBrand
      v-model:visible="showCreateBrand"
      @created="onBrandCreated"
    />

    <!-- Диалог управления атрибутами -->
    <VDialog
      v-model:visible="showAttributeManager"
      modal
      header="Управление атрибутами каталожной позиции"
      class="w-auto flex"
    >
      <AttributeManager
        v-model="currentItemAttributes"
        title="Атрибуты каталожной позиции"
        @close="closeAttributeManager"
      />
    </VDialog>

    <!-- Диалог подтверждения -->
    <VConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useTrpc } from "@/composables/useTrpc";
import { useConfirm } from "@/composables/useConfirm";
import VCard from "@/volt/Card.vue";
import VButton from "@/volt/Button.vue";
import VTag from "@/volt/Tag.vue";
import VSelectButton from "@/volt/SelectButton.vue";
import VAutoComplete from "@/volt/AutoComplete.vue";
import VInputText from "@/volt/InputText.vue";
import VTextarea from "@/volt/Textarea.vue";
import VSelect from "@/volt/Select.vue";
import VDialog from "@/volt/Dialog.vue";
import VConfirmDialog from "@/volt/ConfirmDialog.vue";
import QuickCreateBrand from "../parts/QuickCreateBrand.vue";
import AttributeManager from "@/components/admin/attributes/AttributeManager.vue";
import Icon from "@/components/ui/Icon.vue";
import { PlusIcon } from "lucide-vue-next";
import { UnlinkIcon } from "lucide-vue-next";
import { Trash2Icon } from "lucide-vue-next";
import DangerButton from "@/volt/DangerButton.vue";

// Props
interface Props {
  modelValue: any[];
  mode?: "create" | "edit";
  partId?: number;
}

const props = withDefaults(defineProps<Props>(), {
  mode: "create",
  partId: undefined,
});

// Emits
interface Emits {
  (e: "update:modelValue", value: any[]): void;
}

const emit = defineEmits<Emits>();

// Composables
const { catalogItems, brands, partApplicability, client } = useTrpc();
const confirm = useConfirm();

// Состояние
const catalogItemSuggestions = ref<any[]>([]);
const brandSuggestions = ref<any[]>([]);
const showCreateBrand = ref(false);
const showAttributeManager = ref(false);
const currentItemIndex = ref<number>(-1);
const currentItemAttributes = ref<any[]>([]);

// Опции точности применимости
const accuracyOptions = [
  { label: "Точное совпадение", value: "EXACT_MATCH" },
  { label: "Совпадение с примечаниями", value: "MATCH_WITH_NOTES" },
  { label: "Требует доработки", value: "REQUIRES_MODIFICATION" },
  { label: "Частичное совпадение", value: "PARTIAL_MATCH" },
];

// Опции типа позиции (новая/существующая)
const itemTypeOptions = [
  { label: "Создать новую позицию", value: false },
  { label: "Выбрать существующую", value: true },
];

// Методы
const addCatalogItem = () => {
  const newItem = {
    // Для создания новой позиции
    sku: "",
    brandId: "",
    selectedBrand: null,
    description: "",
    attributes: [], // Добавляем поле для атрибутов

    // Для поиска существующей позиции
    isExisting: false,
    existingCatalogItem: null,

    // Уровень точности применимости
    accuracy: "EXACT_MATCH",
    notes: "",

    // ID применимости (для режима редактирования)
    applicabilityId: undefined,
  };

  const updatedValue = [...props.modelValue, newItem];
  emit("update:modelValue", updatedValue);
};

const removeCatalogItem = (index: number) => {
  const updatedValue = props.modelValue.filter((_, i) => i !== index);
  emit("update:modelValue", updatedValue);
};

const getDisplayLabel = (item: any) => {
  if (!item) return "";
  return typeof item === "object"
    ? `${item.sku} (${item.brand?.name || "Без бренда"})`
    : item;
};

const onItemSelect = (index: number, selectedItem: any) => {
  const updatedValue = [...props.modelValue];
  if (typeof selectedItem === "object") {
    updatedValue[index].existingCatalogItem = selectedItem;
    // При выборе новой существующей позиции сбрасываем applicabilityId
    // поскольку это будет новая связь
    updatedValue[index].applicabilityId = undefined;
  }
  emit("update:modelValue", updatedValue);
};

const searchCatalogItems = async (event: any) => {
  try {
    const query = event.query.toLowerCase();
    const result = await catalogItems.findMany({
      where: {
        OR: [
          { sku: { contains: query, mode: "insensitive" } },
          { brand: { name: { contains: query, mode: "insensitive" } } },
        ],
      },
      include: {
        brand: true,
      },
      take: 10,
    });

    if (result) {
      catalogItemSuggestions.value = result.map((item: any) => ({
        ...item,
        displayLabel: `${item.sku} (${item.brand?.name || "Без бренда"})`,
      }));
    }
  } catch (err) {
    console.error("Ошибка поиска каталожных позиций:", err);
  }
};

const searchBrands = async (event: any) => {
  try {
    const query = event.query.toLowerCase();
    const result = await brands.findMany({
      where: {
        name: { contains: query, mode: "insensitive" },
      },
      take: 10,
    });
    if (result) {
      brandSuggestions.value = result;
    }
  } catch (err) {
    console.error("Ошибка поиска брендов:", err);
  }
};

const onBrandCreated = (brand: any) => {
  brandSuggestions.value = [brand, ...brandSuggestions.value];
};

// Методы для работы с атрибутами
const openAttributeManager = (index: number) => {
  currentItemIndex.value = index;
  currentItemAttributes.value = [
    ...(props.modelValue[index]?.attributes || []),
  ];
  showAttributeManager.value = true;
};

const closeAttributeManager = () => {
  // Сохраняем атрибуты в каталожную позицию
  if (currentItemIndex.value >= 0) {
    const updatedValue = [...props.modelValue];
    updatedValue[currentItemIndex.value].attributes = [
      ...currentItemAttributes.value,
    ];
    emit("update:modelValue", updatedValue);
  }

  showAttributeManager.value = false;
  currentItemIndex.value = -1;
  currentItemAttributes.value = [];
};

const removeAttribute = (itemIndex: number, attrIndex: number) => {
  const updatedValue = [...props.modelValue];
  if (updatedValue[itemIndex].attributes) {
    updatedValue[itemIndex].attributes.splice(attrIndex, 1);
    emit("update:modelValue", updatedValue);
  }
};

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    MM: "мм",
    INCH: "дюймы",
    FT: "футы",
    G: "г",
    KG: "кг",
    T: "т",
    LB: "фунты",
    ML: "мл",
    L: "л",
    GAL: "галлоны",
    PCS: "шт",
    SET: "комплект",
    PAIR: "пара",
    BAR: "бар",
    PSI: "PSI",
    KW: "кВт",
    HP: "л.с.",
    NM: "Н⋅м",
    RPM: "об/мин",
    C: "°C",
    F: "°F",
    PERCENT: "%",
  };
  return labels[unit] || unit;
};

// Методы отвязки и удаления каталожных позиций
const unlinkCatalogItem = async (item: any, index: number) => {
  if (!item.applicabilityId) {
    console.warn("Нет ID применимости для отвязки");
    return;
  }

  const catalogItemName = item.isExisting
    ? `${item.existingCatalogItem?.sku} (${item.existingCatalogItem?.brand?.name})`
    : `${item.sku} (${item.selectedBrand?.name})`;

  confirm.show({
    header: "Отвязать каталожную позицию?",
    message: `Вы уверены, что хотите отвязать позицию "${catalogItemName}" от этой запчасти? Сама позиция останется в системе.`,
    icon: "pi pi-link-slash",
    acceptLabel: "Отвязать",
    rejectLabel: "Отмена",
    acceptClass: "bg-primary-500 hover:bg-primary-600",
    accept: async () => {
      try {
        await partApplicability.delete({
          where: { id: item.applicabilityId! },
        });

        // Удаляем из локального массива
        const updatedValue = props.modelValue.filter((_, i) => i !== index);
        emit("update:modelValue", updatedValue);

        console.log("Каталожная позиция успешно отвязана");
      } catch (err) {
        console.error("Ошибка отвязки каталожной позиции:", err);
      }
    },
  });
};

const deleteCatalogItem = async (item: any, index: number) => {
  const catalogItemName = item.isExisting
    ? `${item.existingCatalogItem?.sku} (${item.existingCatalogItem?.brand?.name})`
    : `${item.sku} (${item.selectedBrand?.name})`;

  const catalogItemId = item.isExisting ? item.existingCatalogItem?.id : null;

  if (!catalogItemId) {
    console.warn("Нет ID каталожной позиции для удаления");
    return;
  }

  confirm.show({
    header: "Удалить каталожную позицию?",
    message: `Вы уверены, что хотите ПОЛНОСТЬЮ УДАЛИТЬ позицию "${catalogItemName}" из системы? Это действие необратимо и может повлиять на другие запчасти, которые связаны с этой позицией.`,
    icon: "pi pi-trash",
    acceptLabel: "Удалить",
    rejectLabel: "Отмена",
    acceptClass: "bg-red-500 hover:bg-red-600",
    accept: async () => {
      try {
        // Сначала отвязываем, если есть ID применимости
        if (item.applicabilityId) {
          await partApplicability.delete({
            where: { id: item.applicabilityId },
          });
        }

        // Затем пытаемся удалить саму каталожную позицию
        await catalogItems.delete({
          where: { id: catalogItemId },
        });

        // Удаляем из локального массива
        const updatedValue = props.modelValue.filter((_, i) => i !== index);
        emit("update:modelValue", updatedValue);

        console.log("Каталожная позиция успешно удалена");
      } catch (err: any) {
        console.error("Ошибка удаления каталожной позиции:", err);

        // Проверяем тип ошибки
        if (
          err?.message?.includes("Foreign key constraint") ||
          err?.message?.includes("violates foreign key") ||
          err?.code === "P2003"
        ) {
          console.error(
            `Невозможно удалить каталожную позицию "${catalogItemName}". Она используется в других запчастях или заказах.`
          );
        }
      }
    },
  });
};
</script>
