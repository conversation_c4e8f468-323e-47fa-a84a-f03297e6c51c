/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { CategoryPageConfigSelectObjectSchema } from '../objects/CategoryPageConfigSelect.schema';
import { CategoryPageConfigIncludeObjectSchema } from '../objects/CategoryPageConfigInclude.schema';
import { CategoryPageConfigWhereUniqueInputObjectSchema } from '../objects/CategoryPageConfigWhereUniqueInput.schema';
import { CategoryPageConfigWhereInputObjectSchema } from '../objects/CategoryPageConfigWhereInput.schema';
import { CategoryPageConfigOrderByWithRelationInputObjectSchema } from '../objects/CategoryPageConfigOrderByWithRelationInput.schema';
import { CategoryPageConfigScalarFieldEnumSchema } from '../enums/CategoryPageConfigScalarFieldEnum.schema';
import { CategoryPageConfigCreateInputObjectSchema } from '../objects/CategoryPageConfigCreateInput.schema';
import { CategoryPageConfigUncheckedCreateInputObjectSchema } from '../objects/CategoryPageConfigUncheckedCreateInput.schema';
import { CategoryPageConfigCreateManyInputObjectSchema } from '../objects/CategoryPageConfigCreateManyInput.schema';
import { CategoryPageConfigUpdateInputObjectSchema } from '../objects/CategoryPageConfigUpdateInput.schema';
import { CategoryPageConfigUncheckedUpdateInputObjectSchema } from '../objects/CategoryPageConfigUncheckedUpdateInput.schema';
import { CategoryPageConfigUpdateManyMutationInputObjectSchema } from '../objects/CategoryPageConfigUpdateManyMutationInput.schema';
import { CategoryPageConfigUncheckedUpdateManyInputObjectSchema } from '../objects/CategoryPageConfigUncheckedUpdateManyInput.schema';
import { CategoryPageConfigCountAggregateInputObjectSchema } from '../objects/CategoryPageConfigCountAggregateInput.schema';
import { CategoryPageConfigMinAggregateInputObjectSchema } from '../objects/CategoryPageConfigMinAggregateInput.schema';
import { CategoryPageConfigMaxAggregateInputObjectSchema } from '../objects/CategoryPageConfigMaxAggregateInput.schema';
import { CategoryPageConfigAvgAggregateInputObjectSchema } from '../objects/CategoryPageConfigAvgAggregateInput.schema';
import { CategoryPageConfigSumAggregateInputObjectSchema } from '../objects/CategoryPageConfigSumAggregateInput.schema';
import { CategoryPageConfigOrderByWithAggregationInputObjectSchema } from '../objects/CategoryPageConfigOrderByWithAggregationInput.schema';
import { CategoryPageConfigScalarWhereWithAggregatesInputObjectSchema } from '../objects/CategoryPageConfigScalarWhereWithAggregatesInput.schema'

type CategoryPageConfigInputSchemaType = {
    findUnique: z.ZodType<Prisma.CategoryPageConfigFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.CategoryPageConfigFindFirstArgs>,
    findMany: z.ZodType<Prisma.CategoryPageConfigFindManyArgs>,
    create: z.ZodType<Prisma.CategoryPageConfigCreateArgs>,
    createMany: z.ZodType<Prisma.CategoryPageConfigCreateManyArgs>,
    delete: z.ZodType<Prisma.CategoryPageConfigDeleteArgs>,
    deleteMany: z.ZodType<Prisma.CategoryPageConfigDeleteManyArgs>,
    update: z.ZodType<Prisma.CategoryPageConfigUpdateArgs>,
    updateMany: z.ZodType<Prisma.CategoryPageConfigUpdateManyArgs>,
    upsert: z.ZodType<Prisma.CategoryPageConfigUpsertArgs>,
    aggregate: z.ZodType<Prisma.CategoryPageConfigAggregateArgs>,
    groupBy: z.ZodType<Prisma.CategoryPageConfigGroupByArgs>,
    count: z.ZodType<Prisma.CategoryPageConfigCountArgs>
}

export const CategoryPageConfigInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => CategoryPageConfigSelectObjectSchema.optional()), include: z.lazy(() => CategoryPageConfigIncludeObjectSchema.optional()), where: CategoryPageConfigWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => CategoryPageConfigSelectObjectSchema.optional()), include: z.lazy(() => CategoryPageConfigIncludeObjectSchema.optional()), where: CategoryPageConfigWhereInputObjectSchema.optional(), orderBy: z.union([CategoryPageConfigOrderByWithRelationInputObjectSchema, CategoryPageConfigOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CategoryPageConfigWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(CategoryPageConfigScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => CategoryPageConfigSelectObjectSchema.optional()), include: z.lazy(() => CategoryPageConfigIncludeObjectSchema.optional()), where: CategoryPageConfigWhereInputObjectSchema.optional(), orderBy: z.union([CategoryPageConfigOrderByWithRelationInputObjectSchema, CategoryPageConfigOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CategoryPageConfigWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(CategoryPageConfigScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => CategoryPageConfigSelectObjectSchema.optional()), include: z.lazy(() => CategoryPageConfigIncludeObjectSchema.optional()), data: z.union([CategoryPageConfigCreateInputObjectSchema, CategoryPageConfigUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([CategoryPageConfigCreateManyInputObjectSchema, z.array(CategoryPageConfigCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => CategoryPageConfigSelectObjectSchema.optional()), include: z.lazy(() => CategoryPageConfigIncludeObjectSchema.optional()), where: CategoryPageConfigWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: CategoryPageConfigWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => CategoryPageConfigSelectObjectSchema.optional()), include: z.lazy(() => CategoryPageConfigIncludeObjectSchema.optional()), data: z.union([CategoryPageConfigUpdateInputObjectSchema, CategoryPageConfigUncheckedUpdateInputObjectSchema]), where: CategoryPageConfigWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([CategoryPageConfigUpdateManyMutationInputObjectSchema, CategoryPageConfigUncheckedUpdateManyInputObjectSchema]), where: CategoryPageConfigWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => CategoryPageConfigSelectObjectSchema.optional()), include: z.lazy(() => CategoryPageConfigIncludeObjectSchema.optional()), where: CategoryPageConfigWhereUniqueInputObjectSchema, create: z.union([CategoryPageConfigCreateInputObjectSchema, CategoryPageConfigUncheckedCreateInputObjectSchema]), update: z.union([CategoryPageConfigUpdateInputObjectSchema, CategoryPageConfigUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: CategoryPageConfigWhereInputObjectSchema.optional(), orderBy: z.union([CategoryPageConfigOrderByWithRelationInputObjectSchema, CategoryPageConfigOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CategoryPageConfigWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), CategoryPageConfigCountAggregateInputObjectSchema]).optional(), _min: CategoryPageConfigMinAggregateInputObjectSchema.optional(), _max: CategoryPageConfigMaxAggregateInputObjectSchema.optional(), _avg: CategoryPageConfigAvgAggregateInputObjectSchema.optional(), _sum: CategoryPageConfigSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: CategoryPageConfigWhereInputObjectSchema.optional(), orderBy: z.union([CategoryPageConfigOrderByWithAggregationInputObjectSchema, CategoryPageConfigOrderByWithAggregationInputObjectSchema.array()]).optional(), having: CategoryPageConfigScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(CategoryPageConfigScalarFieldEnumSchema), _count: z.union([z.literal(true), CategoryPageConfigCountAggregateInputObjectSchema]).optional(), _min: CategoryPageConfigMinAggregateInputObjectSchema.optional(), _max: CategoryPageConfigMaxAggregateInputObjectSchema.optional(), _avg: CategoryPageConfigAvgAggregateInputObjectSchema.optional(), _sum: CategoryPageConfigSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: CategoryPageConfigWhereInputObjectSchema.optional(), orderBy: z.union([CategoryPageConfigOrderByWithRelationInputObjectSchema, CategoryPageConfigOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CategoryPageConfigWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(CategoryPageConfigScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), CategoryPageConfigCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as CategoryPageConfigInputSchemaType;
