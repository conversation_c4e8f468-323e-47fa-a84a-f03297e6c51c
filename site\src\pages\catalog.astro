---
import MainLayout from "../layouts/MainLayout.astro";
import SearchHeaderIsland from "@/components/catalog/islands/SearchHeaderIsland";
import FiltersIsland from "@/components/catalog/islands/FiltersIsland";
import ResultsIsland from "@/components/catalog/islands/ResultsIsland";
---

<MainLayout title="Каталог запчастей" description="Поиск и просмотр каталога взаимозаменяемых запчастей">
  <div class="min-h-screen bg-background">
    <SearchHeaderIsland client:load />
    <div class="flex flex-col md:flex-row">
      <!-- Десктопная версия фильтров -->
      <div class="hidden md:block">
        <FiltersIsland client:visible />
      </div>
      <!-- Мобильная версия - фильтры будут в drawer внутри ResultsIsland -->
      <ResultsIsland client:load />
    </div>
  </div>
</MainLayout>
