/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryCreateWithoutPageConfigInputObjectSchema } from './PartCategoryCreateWithoutPageConfigInput.schema';
import { PartCategoryUncheckedCreateWithoutPageConfigInputObjectSchema } from './PartCategoryUncheckedCreateWithoutPageConfigInput.schema';
import { PartCategoryCreateOrConnectWithoutPageConfigInputObjectSchema } from './PartCategoryCreateOrConnectWithoutPageConfigInput.schema';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCreateNestedOneWithoutPageConfigInput>;
export const PartCategoryCreateNestedOneWithoutPageConfigInputObjectSchema: SchemaType = z.object({
    create: z.union([z.lazy(() => PartCategoryCreateWithoutPageConfigInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutPageConfigInputObjectSchema)]).optional(), connectOrCreate: z.lazy(() => PartCategoryCreateOrConnectWithoutPageConfigInputObjectSchema).optional().optional(), connect: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema).optional().optional()
}).strict() as SchemaType;
