import { z } from 'zod'
import { router, publicProcedure } from '../trpc'
import type { Context } from '../trpc'
import { SiteService, SearchCatalogItemsSiteInput, type SearchCatalogItemsSiteInputType, SearchPartsSiteInput, type SearchPartsSiteInputType } from '../services/site.service'

export const siteRouter = router({
  search: router({
    catalogItems: publicProcedure
      .input(SearchCatalogItemsSiteInput)
      .query(async ({ ctx, input }: { ctx: Context; input: SearchCatalogItemsSiteInputType }) => SiteService.searchCatalogItems(ctx, input)),
    
    parts: publicProcedure
      .input(SearchPartsSiteInput)
      .query(async ({ ctx, input }: { ctx: Context; input: SearchPartsSiteInputType }) => SiteService.searchParts(ctx, input)),
  }),

  catalog: router({
    brands: publicProcedure
      .input(z.object({ take: z.number().min(1).max(200).default(100) }).optional())
      .query(async ({ ctx, input }: { ctx: Context; input?: { take?: number } }) => SiteService.brands(ctx, input ?? undefined)),

    categories: publicProcedure
      .input(z.object({ rootOnly: z.boolean().default(false), take: z.number().min(1).max(500).default(200) }).optional())
      .query(async ({ ctx, input }: { ctx: Context; input?: { rootOnly?: boolean; take?: number } }) => SiteService.categories(ctx, input ?? undefined)),
  }),

  attributes: router({
    templates: publicProcedure
      .input(z.object({ take: z.number().min(1).max(500).default(100) }).optional())
      .query(async ({ ctx, input }: { ctx: Context; input?: { take?: number } }) => SiteService.attributeTemplates(ctx, input ?? undefined)),

    stats: publicProcedure
      .input(z.object({ templateId: z.number() }))
      .query(async ({ ctx, input }: { ctx: Context; input: { templateId: number } }) => SiteService.attributeStats(ctx, { templateId: input.templateId })),
  }),
})

