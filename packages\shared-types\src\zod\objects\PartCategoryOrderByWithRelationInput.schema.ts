/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { PartCategoryOrderByRelationAggregateInputObjectSchema } from './PartCategoryOrderByRelationAggregateInput.schema';
import { PartOrderByRelationAggregateInputObjectSchema } from './PartOrderByRelationAggregateInput.schema';
import { CategoryPageConfigOrderByWithRelationInputObjectSchema } from './CategoryPageConfigOrderByWithRelationInput.schema';
import { MediaAssetOrderByWithRelationInputObjectSchema } from './MediaAssetOrderByWithRelationInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryOrderByWithRelationInput>;
export const PartCategoryOrderByWithRelationInputObjectSchema: SchemaType = z.object({
    id: z.lazy(() => SortOrderSchema).optional().optional(), name: z.lazy(() => SortOrderSchema).optional().optional(), slug: z.lazy(() => SortOrderSchema).optional().optional(), description: z.union([z.lazy(() => SortOrderSchema),
    z.lazy(() => SortOrderInputObjectSchema)]).optional(), parentId: z.union([z.lazy(() => SortOrderSchema),
    z.lazy(() => SortOrderInputObjectSchema)]).optional(), level: z.lazy(() => SortOrderSchema).optional().optional(), path: z.lazy(() => SortOrderSchema).optional().optional(), icon: z.union([z.lazy(() => SortOrderSchema),
    z.lazy(() => SortOrderInputObjectSchema)]).optional(), imageId: z.union([z.lazy(() => SortOrderSchema),
    z.lazy(() => SortOrderInputObjectSchema)]).optional(), createdAt: z.lazy(() => SortOrderSchema).optional().optional(), updatedAt: z.lazy(() => SortOrderSchema).optional().optional(), parent: z.lazy(() => PartCategoryOrderByWithRelationInputObjectSchema).optional().optional(), children: z.lazy(() => PartCategoryOrderByRelationAggregateInputObjectSchema).optional().optional(), parts: z.lazy(() => PartOrderByRelationAggregateInputObjectSchema).optional().optional(), pageConfig: z.lazy(() => CategoryPageConfigOrderByWithRelationInputObjectSchema).optional().optional(), image: z.lazy(() => MediaAssetOrderByWithRelationInputObjectSchema).optional().optional()
}).strict() as SchemaType;
