// This is a workaround for a bug in <PERSON><PERSON>'s type generation
// TODO: remove this when the bug is fixed
import type { Part as PrismaPart, PartCategory, MediaAsset, PartAttribute, AttributeTemplate, PartApplicability, CatalogItem, Brand, CatalogItemAttribute } from "packages/shared-types/src/index";

type FullAttributeTemplate = AttributeTemplate & {
  synonymGroups: (AttributeSynonymGroup & { synonyms: AttributeSynonym[] })[]
};
type FullPartAttribute = PartAttribute & { template: FullAttributeTemplate };
type FullCatalogItemAttribute = CatalogItemAttribute & { template: FullAttributeTemplate };

type FullCatalogItem = CatalogItem & {
  brand: Brand;
  image: MediaAsset | null;
  mediaAssets: MediaAsset[];
  attributes: FullCatalogItemAttribute[];
};

type FullPartApplicability = PartApplicability & {
  catalogItem: FullCatalogItem;
};

export type Part = PrismaPart & {
  partCategory: PartCategory | null;
  image: MediaAsset | null;
  mediaAssets: MediaAsset[];
  attributes: FullPartAttribute[];
  applicabilities: FullPartApplicability[];
};
