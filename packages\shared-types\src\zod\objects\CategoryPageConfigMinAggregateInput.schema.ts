/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigMinAggregateInputType>;
export const CategoryPageConfigMinAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), categoryId: z.literal(true).optional().optional(), listTemplateSlug: z.literal(true).optional().optional(), detailTemplateSlug: z.literal(true).optional().optional(), enabled: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional()
}).strict() as SchemaType;
