/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SortOrderSchema } from '../enums/SortOrder.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PageTemplateMaxOrderByAggregateInput>;
export const PageTemplateMaxOrderByAggregateInputObjectSchema: SchemaType = z.object({
    id: z.lazy(() => SortOrderSchema).optional().optional(), area: z.lazy(() => SortOrderSchema).optional().optional(), slug: z.lazy(() => SortOrderSchema).optional().optional(), title: z.lazy(() => SortOrderSchema).optional().optional(), description: z.lazy(() => SortOrderSchema).optional().optional(), enabled: z.lazy(() => SortOrderSchema).optional().optional(), createdAt: z.lazy(() => SortOrderSchema).optional().optional(), updatedAt: z.lazy(() => SortOrderSchema).optional().optional()
}).strict() as SchemaType;
