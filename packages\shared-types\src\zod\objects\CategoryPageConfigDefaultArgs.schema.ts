/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CategoryPageConfigSelectObjectSchema } from './CategoryPageConfigSelect.schema';
import { CategoryPageConfigIncludeObjectSchema } from './CategoryPageConfigInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigDefaultArgs>;
export const CategoryPageConfigDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => CategoryPageConfigSelectObjectSchema).optional().optional(), include: z.lazy(() => CategoryPageConfigIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
