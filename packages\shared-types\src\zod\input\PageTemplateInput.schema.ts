/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { PageTemplateSelectObjectSchema } from '../objects/PageTemplateSelect.schema';
;
import { PageTemplateWhereUniqueInputObjectSchema } from '../objects/PageTemplateWhereUniqueInput.schema';
import { PageTemplateWhereInputObjectSchema } from '../objects/PageTemplateWhereInput.schema';
import { PageTemplateOrderByWithRelationInputObjectSchema } from '../objects/PageTemplateOrderByWithRelationInput.schema';
import { PageTemplateScalarFieldEnumSchema } from '../enums/PageTemplateScalarFieldEnum.schema';
import { PageTemplateCreateInputObjectSchema } from '../objects/PageTemplateCreateInput.schema';
import { PageTemplateUncheckedCreateInputObjectSchema } from '../objects/PageTemplateUncheckedCreateInput.schema';
import { PageTemplateCreateManyInputObjectSchema } from '../objects/PageTemplateCreateManyInput.schema';
import { PageTemplateUpdateInputObjectSchema } from '../objects/PageTemplateUpdateInput.schema';
import { PageTemplateUncheckedUpdateInputObjectSchema } from '../objects/PageTemplateUncheckedUpdateInput.schema';
import { PageTemplateUpdateManyMutationInputObjectSchema } from '../objects/PageTemplateUpdateManyMutationInput.schema';
import { PageTemplateUncheckedUpdateManyInputObjectSchema } from '../objects/PageTemplateUncheckedUpdateManyInput.schema';
import { PageTemplateCountAggregateInputObjectSchema } from '../objects/PageTemplateCountAggregateInput.schema';
import { PageTemplateMinAggregateInputObjectSchema } from '../objects/PageTemplateMinAggregateInput.schema';
import { PageTemplateMaxAggregateInputObjectSchema } from '../objects/PageTemplateMaxAggregateInput.schema';
import { PageTemplateAvgAggregateInputObjectSchema } from '../objects/PageTemplateAvgAggregateInput.schema';
import { PageTemplateSumAggregateInputObjectSchema } from '../objects/PageTemplateSumAggregateInput.schema';
import { PageTemplateOrderByWithAggregationInputObjectSchema } from '../objects/PageTemplateOrderByWithAggregationInput.schema';
import { PageTemplateScalarWhereWithAggregatesInputObjectSchema } from '../objects/PageTemplateScalarWhereWithAggregatesInput.schema'

type PageTemplateInputSchemaType = {
    findUnique: z.ZodType<Prisma.PageTemplateFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.PageTemplateFindFirstArgs>,
    findMany: z.ZodType<Prisma.PageTemplateFindManyArgs>,
    create: z.ZodType<Prisma.PageTemplateCreateArgs>,
    createMany: z.ZodType<Prisma.PageTemplateCreateManyArgs>,
    delete: z.ZodType<Prisma.PageTemplateDeleteArgs>,
    deleteMany: z.ZodType<Prisma.PageTemplateDeleteManyArgs>,
    update: z.ZodType<Prisma.PageTemplateUpdateArgs>,
    updateMany: z.ZodType<Prisma.PageTemplateUpdateManyArgs>,
    upsert: z.ZodType<Prisma.PageTemplateUpsertArgs>,
    aggregate: z.ZodType<Prisma.PageTemplateAggregateArgs>,
    groupBy: z.ZodType<Prisma.PageTemplateGroupByArgs>,
    count: z.ZodType<Prisma.PageTemplateCountArgs>
}

export const PageTemplateInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => PageTemplateSelectObjectSchema.optional()), where: PageTemplateWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => PageTemplateSelectObjectSchema.optional()), where: PageTemplateWhereInputObjectSchema.optional(), orderBy: z.union([PageTemplateOrderByWithRelationInputObjectSchema, PageTemplateOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PageTemplateWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PageTemplateScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => PageTemplateSelectObjectSchema.optional()), where: PageTemplateWhereInputObjectSchema.optional(), orderBy: z.union([PageTemplateOrderByWithRelationInputObjectSchema, PageTemplateOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PageTemplateWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PageTemplateScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => PageTemplateSelectObjectSchema.optional()), data: z.union([PageTemplateCreateInputObjectSchema, PageTemplateUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([PageTemplateCreateManyInputObjectSchema, z.array(PageTemplateCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => PageTemplateSelectObjectSchema.optional()), where: PageTemplateWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: PageTemplateWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => PageTemplateSelectObjectSchema.optional()), data: z.union([PageTemplateUpdateInputObjectSchema, PageTemplateUncheckedUpdateInputObjectSchema]), where: PageTemplateWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([PageTemplateUpdateManyMutationInputObjectSchema, PageTemplateUncheckedUpdateManyInputObjectSchema]), where: PageTemplateWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => PageTemplateSelectObjectSchema.optional()), where: PageTemplateWhereUniqueInputObjectSchema, create: z.union([PageTemplateCreateInputObjectSchema, PageTemplateUncheckedCreateInputObjectSchema]), update: z.union([PageTemplateUpdateInputObjectSchema, PageTemplateUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: PageTemplateWhereInputObjectSchema.optional(), orderBy: z.union([PageTemplateOrderByWithRelationInputObjectSchema, PageTemplateOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PageTemplateWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), PageTemplateCountAggregateInputObjectSchema]).optional(), _min: PageTemplateMinAggregateInputObjectSchema.optional(), _max: PageTemplateMaxAggregateInputObjectSchema.optional(), _avg: PageTemplateAvgAggregateInputObjectSchema.optional(), _sum: PageTemplateSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: PageTemplateWhereInputObjectSchema.optional(), orderBy: z.union([PageTemplateOrderByWithAggregationInputObjectSchema, PageTemplateOrderByWithAggregationInputObjectSchema.array()]).optional(), having: PageTemplateScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(PageTemplateScalarFieldEnumSchema), _count: z.union([z.literal(true), PageTemplateCountAggregateInputObjectSchema]).optional(), _min: PageTemplateMinAggregateInputObjectSchema.optional(), _max: PageTemplateMaxAggregateInputObjectSchema.optional(), _avg: PageTemplateAvgAggregateInputObjectSchema.optional(), _sum: PageTemplateSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: PageTemplateWhereInputObjectSchema.optional(), orderBy: z.union([PageTemplateOrderByWithRelationInputObjectSchema, PageTemplateOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PageTemplateWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PageTemplateScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), PageTemplateCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as PageTemplateInputSchemaType;
