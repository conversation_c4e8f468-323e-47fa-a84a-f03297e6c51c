/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { TemplateAreaSchema } from '../enums/TemplateArea.schema';
import { JsonNullValueInputSchema } from '../enums/JsonNullValueInput.schema';

import type { Prisma } from '@prisma/client';


const literalSchema = z.union([z.string(), z.number(), z.boolean()]);
const jsonSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
    z.union([literalSchema, z.array(jsonSchema.nullable()), z.record(jsonSchema.nullable())])
);

type SchemaType = z.ZodType<Prisma.PageTemplateCreateInput>;
export const PageTemplateCreateInputObjectSchema: SchemaType = z.object({
    area: z.lazy(() => TemplateAreaSchema), slug: z.string(), title: z.string(), description: z.union([z.string(),
    z.null()]).optional().nullable(), defaultConfig: z.union([z.lazy(() => JsonNullValueInputSchema),
        jsonSchema]), enabled: z.boolean().optional().optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional()
}).strict() as SchemaType;
