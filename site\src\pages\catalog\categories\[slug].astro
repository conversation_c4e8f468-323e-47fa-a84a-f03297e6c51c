---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { PartCard } from "@/components/catalog/PartCard";
import { CategoryCard } from "@/components/catalog/CategoryCard";
import { renderTemplateString } from "@/lib/catalog";

const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/categories');
}

// Загружаем категорию и связанные данные
let category: any = null;
let parts: any[] = [];
let subcategories: any[] = [];
type PageConfigResponse = {
  category: { id: number; name: string; slug: string };
  config: null | { textTemplates?: { list?: { h1?: string; title?: string; metaDescription?: string } } };
  allowed: { listTemplates: string[]; detailTemplates: string[]; listEntities: string[] };
} | null;
let pageConfig: PageConfigResponse = null;
let h1: string = '';
let metaTitle: string = '';
let metaDescription: string = '';

try {
  // Получаем категорию по slug
  const categoryResponse = await trpcClient.crud.partCategory.findUnique.query({
    where: { slug },
    include: {
      image: true,
      parent: true,
      children: {
        include: {
          image: true,
          _count: { select: { parts: true } }
        },
        orderBy: { name: 'asc' }
      },
      _count: { select: { parts: true } }
    }
  });
  
  if (!categoryResponse) {
    return Astro.redirect('/categories');
  }
  
  category = categoryResponse;
  subcategories = category.children || [];

  // Загружаем конфиг страницы категории через CRUD (минимальная зависимость)
  const cfg = await trpcClient.crud.categoryPageConfig.findUnique.query({ where: { categoryId: category.id } });
  pageConfig = cfg
    ? {
        category: { id: category.id, name: category.name, slug: category.slug },
        config: cfg as any,
        allowed: { listTemplates: ['grid-basic','table-compact'], detailTemplates: ['spec-two-column','tabs'], listEntities: ['PART','ITEM'] },
      }
    : {
        category: { id: category.id, name: category.name, slug: category.slug },
        config: null,
        allowed: { listTemplates: ['grid-basic','table-compact'], detailTemplates: ['spec-two-column','tabs'], listEntities: ['PART','ITEM'] },
      };

  // Получаем запчасти этой категории
  const partsResponse = await trpcClient.crud.part.findMany.query({
    where: { partCategoryId: category.id },
    include: {
      partCategory: true,
      image: true,
      attributes: {
        include: { template: true },
        take: 3
      }
    },
    take: 20,
    orderBy: { updatedAt: 'desc' }
  });
  parts = partsResponse || [];

} catch (error) {
  console.error('Error loading category data:', error);
  return Astro.redirect('/categories');
}

// Рендер текстов H1/Title/Description по конфигу
const listTexts = pageConfig?.config?.textTemplates?.list ?? {};
const vars = { category: category?.name } as Record<string, string>;
h1 = renderTemplateString(listTexts.h1 ?? category?.name ?? '', vars);
metaTitle = renderTemplateString(listTexts.title ?? category?.name ?? '', vars);
metaDescription = renderTemplateString(listTexts.metaDescription ?? (category?.description || `Каталог ${category?.name}`), vars);
---

<MainLayout title={metaTitle || category.name} description={metaDescription || category.description || `Запчасти категории ${category.name}`}>
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Хлебные крошки -->
      <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
              Главная
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <a href="/categories" class="text-sm font-medium text-muted-foreground hover:text-foreground">Категории</a>
            </div>
          </li>
          {category.parent && (
            <li>
              <div class="flex items-center">
                <span class="mx-2 text-muted-foreground">/</span>
                <a href={`/catalog/categories/${category.parent.slug}`} class="text-sm font-medium text-muted-foreground hover:text-foreground">
                  {category.parent.name}
                </a>
              </div>
            </li>
          )}
          <li aria-current="page">
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <span class="text-sm font-medium text-foreground">{category.name}</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Заголовок категории -->
      <div class="mb-8">
        <div class="flex items-start gap-4">
          {category.image && (
            <img
              src={category.image.url}
              alt={category.name}
              class="w-20 h-20 object-cover rounded-lg"
            />
          )}
          <div class="flex-1">
            <h1 class="text-3xl font-bold tracking-tight mb-2 flex items-center gap-2">
              {category.icon && (
                <span class="text-3xl">{category.icon}</span>
              )}
              {h1 || category.name}
            </h1>
            {category.description && (
              <p class="text-muted-foreground mb-4">{category.description}</p>
            )}
            <div class="flex items-center gap-4 text-sm text-muted-foreground">
              <span>Уровень: {category.level}</span>
              <span>Запчастей: {category._count.parts}</span>
              {subcategories.length > 0 && (
                <span>Подкатегорий: {subcategories.length}</span>
              )}
            </div>
          </div>
        </div>
      </div>

      <!-- Подкатегории -->
      {subcategories.length > 0 && (
        <div class="mb-12">
          <h2 class="text-2xl font-semibold mb-6">Подкатегории</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {subcategories.map((subcategory) => (
              <CategoryCard category={subcategory} client:load />
            ))}
          </div>
        </div>
      )}

      <!-- Запчасти категории -->
      <div>
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-semibold">
            Запчасти {category.name.toLowerCase()}
          </h2>
          {parts.length === 20 && (
            <a 
              href={`/catalog?category=${category.id}`}
              class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
            >
              Показать все
            </a>
          )}
        </div>

        {parts.length > 0 ? (
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {parts.map((part) => (
              <PartCard part={part} client:load />
            ))}
          </div>
        ) : (
          <div class="text-center py-12">
            <h3 class="text-lg font-semibold mb-2">Запчасти не найдены</h3>
            <p class="text-muted-foreground mb-4">
              В этой категории пока нет запчастей
            </p>
            {subcategories.length > 0 ? (
              <p class="text-sm text-muted-foreground">
                Попробуйте поискать в подкатегориях выше
              </p>
            ) : (
              <a 
                href="/catalog" 
                class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2"
              >
                Перейти к каталогу
              </a>
            )}
          </div>
        )}
      </div>
    </div>
  </TrpcProvider>
</MainLayout>
