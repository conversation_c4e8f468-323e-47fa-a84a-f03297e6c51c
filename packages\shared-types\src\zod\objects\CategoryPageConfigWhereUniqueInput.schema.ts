/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CategoryPageConfigWhereInputObjectSchema } from './CategoryPageConfigWhereInput.schema';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { JsonFilterObjectSchema } from './JsonFilter.schema';
import { JsonNullableFilterObjectSchema } from './JsonNullableFilter.schema';
import { BoolFilterObjectSchema } from './BoolFilter.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';
import { PartCategoryScalarRelationFilterObjectSchema } from './PartCategoryScalarRelationFilter.schema';
import { PartCategoryWhereInputObjectSchema } from './PartCategoryWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigWhereUniqueInput>;
export const CategoryPageConfigWhereUniqueInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), categoryId: z.number().optional().optional(), AND: z.union([z.lazy(() => CategoryPageConfigWhereInputObjectSchema),
    z.lazy(() => CategoryPageConfigWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => CategoryPageConfigWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => CategoryPageConfigWhereInputObjectSchema),
    z.lazy(() => CategoryPageConfigWhereInputObjectSchema).array()]).optional(), listTemplateSlug: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), detailTemplateSlug: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), listConfig: z.lazy(() => JsonFilterObjectSchema).optional().optional(), detailConfig: z.lazy(() => JsonFilterObjectSchema).optional().optional(), variables: z.lazy(() => JsonFilterObjectSchema).optional().optional(), textTemplates: z.lazy(() => JsonFilterObjectSchema).optional().optional(), seo: z.lazy(() => JsonNullableFilterObjectSchema).optional().optional(), enabled: z.union([z.lazy(() => BoolFilterObjectSchema),
    z.boolean()]).optional(), createdAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), category: z.union([z.lazy(() => PartCategoryScalarRelationFilterObjectSchema),
    z.lazy(() => PartCategoryWhereInputObjectSchema)]).optional()
}).strict() as SchemaType;
