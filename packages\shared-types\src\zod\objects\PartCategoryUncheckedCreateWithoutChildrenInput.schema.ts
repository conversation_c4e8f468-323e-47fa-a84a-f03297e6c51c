/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUncheckedCreateNestedManyWithoutPartCategoryInputObjectSchema } from './PartUncheckedCreateNestedManyWithoutPartCategoryInput.schema';
import { CategoryPageConfigUncheckedCreateNestedOneWithoutCategoryInputObjectSchema } from './CategoryPageConfigUncheckedCreateNestedOneWithoutCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUncheckedCreateWithoutChildrenInput>;
export const PartCategoryUncheckedCreateWithoutChildrenInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), name: z.string(), slug: z.string(), description: z.union([z.string(),
    z.null()]).optional().nullable(), parentId: z.union([z.number(),
    z.null()]).optional().nullable(), level: z.number().optional().optional(), path: z.string(), icon: z.union([z.string(),
    z.null()]).optional().nullable(), imageId: z.union([z.number(),
    z.null()]).optional().nullable(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), parts: z.lazy(() => PartUncheckedCreateNestedManyWithoutPartCategoryInputObjectSchema).optional().optional(), pageConfig: z.lazy(() => CategoryPageConfigUncheckedCreateNestedOneWithoutCategoryInputObjectSchema).optional().optional()
}).strict() as SchemaType;
