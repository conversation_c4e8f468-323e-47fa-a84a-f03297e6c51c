/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PageTemplateSelectObjectSchema } from './PageTemplateSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PageTemplateDefaultArgs>;
export const PageTemplateDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => PageTemplateSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
