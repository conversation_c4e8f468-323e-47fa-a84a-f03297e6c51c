/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryUpdateWithoutPageConfigInputObjectSchema } from './PartCategoryUpdateWithoutPageConfigInput.schema';
import { PartCategoryUncheckedUpdateWithoutPageConfigInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutPageConfigInput.schema';
import { PartCategoryCreateWithoutPageConfigInputObjectSchema } from './PartCategoryCreateWithoutPageConfigInput.schema';
import { PartCategoryUncheckedCreateWithoutPageConfigInputObjectSchema } from './PartCategoryUncheckedCreateWithoutPageConfigInput.schema';
import { PartCategoryWhereInputObjectSchema } from './PartCategoryWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpsertWithoutPageConfigInput>;
export const PartCategoryUpsertWithoutPageConfigInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartCategoryUpdateWithoutPageConfigInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutPageConfigInputObjectSchema)]), create: z.union([z.lazy(() => PartCategoryCreateWithoutPageConfigInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutPageConfigInputObjectSchema)]), where: z.lazy(() => PartCategoryWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
