/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
const baseSchema = z.object({
    id: z.number(),
    name: z.string().min(1).max(150),
    slug: z.string().regex(new RegExp("^[a-z0-9-]+$")),
    description: z.string().min(1).max(1000).nullish(),
    level: z.number().default(0),
    path: z.string(),
    icon: z.string().nullish(),
    createdAt: z.coerce.date().default(() => new Date()),
    updatedAt: z.coerce.date(),
}
).strict();
const relationSchema = z.object({
    parent: z.record(z.unknown()).optional(),
    children: z.array(z.unknown()).optional(),
    parts: z.array(z.unknown()).optional(),
    pageConfig: z.record(z.unknown()).optional(),
    image: z.record(z.unknown()).optional(),
}
);
const fkSchema = z.object({
    parentId: z.number().nullish(),
    imageId: z.number().nullish(),
}
);

/**
 * `PartCategory` schema excluding foreign keys and relations.
 */
export const PartCategoryScalarSchema = baseSchema;


/**
 * `PartCategory` schema including all fields (scalar, foreign key, and relations) and validations.
 */
export const PartCategorySchema = PartCategoryScalarSchema.merge(fkSchema).merge(relationSchema.partial());


/**
 * Schema used for validating Prisma create input. For internal use only.
 * @private
 */
export const PartCategoryPrismaCreateSchema = baseSchema.partial().passthrough();


/**
 * Schema used for validating Prisma update input. For internal use only.
 * @private
 */
export const PartCategoryPrismaUpdateSchema = z.object({
    id: z.union([z.number(), z.record(z.unknown())]),
    name: z.string().min(1).max(150),
    slug: z.string().regex(new RegExp("^[a-z0-9-]+$")),
    description: z.string().min(1).max(1000).nullish(),
    level: z.union([z.number().default(0), z.record(z.unknown())]),
    path: z.string(),
    icon: z.string().nullish(),
    createdAt: z.coerce.date().default(() => new Date()),
    updatedAt: z.coerce.date()
}).partial().passthrough();


/**
 * `PartCategory` schema for create operations excluding foreign keys and relations.
 */
export const PartCategoryCreateScalarSchema = baseSchema.partial({
    id: true, level: true, createdAt: true, updatedAt: true
});


/**
 * `PartCategory` schema for create operations including scalar fields, foreign key fields, and validations.
 */
export const PartCategoryCreateSchema = PartCategoryCreateScalarSchema.merge(fkSchema);


/**
 * `PartCategory` schema for update operations excluding foreign keys and relations.
 */
export const PartCategoryUpdateScalarSchema = baseSchema.partial();


/**
 * `PartCategory` schema for update operations including scalar fields, foreign key fields, and validations.
 */
export const PartCategoryUpdateSchema = PartCategoryUpdateScalarSchema.merge(fkSchema.partial());

