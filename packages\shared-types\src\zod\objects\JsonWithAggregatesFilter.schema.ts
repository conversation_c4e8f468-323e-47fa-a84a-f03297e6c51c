/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { JsonNullValueFilterSchema } from '../enums/JsonNullValueFilter.schema';
import { QueryModeSchema } from '../enums/QueryMode.schema';
import { NestedIntFilterObjectSchema } from './NestedIntFilter.schema';
import { NestedJsonFilterObjectSchema } from './NestedJsonFilter.schema';

import type { Prisma } from '@prisma/client';


const literalSchema = z.union([z.string(), z.number(), z.boolean()]);
const jsonSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
    z.union([literalSchema, z.array(jsonSchema.nullable()), z.record(jsonSchema.nullable())])
);

type SchemaType = z.ZodType<Prisma.JsonWithAggregatesFilter>;
export const JsonWithAggregatesFilterObjectSchema: SchemaType = z.object({
    equals: z.union([jsonSchema,
        z.lazy(() => JsonNullValueFilterSchema)]).optional(), path: z.string().array().optional().optional(), mode: z.lazy(() => QueryModeSchema).optional().optional(), string_contains: z.string().optional().optional(), string_starts_with: z.string().optional().optional(), string_ends_with: z.string().optional().optional(), array_starts_with: z.union([jsonSchema,
            z.null()]).optional().nullable(), array_ends_with: z.union([jsonSchema,
                z.null()]).optional().nullable(), array_contains: z.union([jsonSchema,
                    z.null()]).optional().nullable(), lt: jsonSchema.optional().optional(), lte: jsonSchema.optional().optional(), gt: jsonSchema.optional().optional(), gte: jsonSchema.optional().optional(), not: z.union([jsonSchema,
                        z.lazy(() => JsonNullValueFilterSchema)]).optional(), _count: z.lazy(() => NestedIntFilterObjectSchema).optional().optional(), _min: z.lazy(() => NestedJsonFilterObjectSchema).optional().optional(), _max: z.lazy(() => NestedJsonFilterObjectSchema).optional().optional()
}).strict() as SchemaType;
