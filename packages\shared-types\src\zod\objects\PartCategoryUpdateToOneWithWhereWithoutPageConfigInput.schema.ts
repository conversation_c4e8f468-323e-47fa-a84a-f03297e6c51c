/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereInputObjectSchema } from './PartCategoryWhereInput.schema';
import { PartCategoryUpdateWithoutPageConfigInputObjectSchema } from './PartCategoryUpdateWithoutPageConfigInput.schema';
import { PartCategoryUncheckedUpdateWithoutPageConfigInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutPageConfigInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpdateToOneWithWhereWithoutPageConfigInput>;
export const PartCategoryUpdateToOneWithWhereWithoutPageConfigInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartCategoryUpdateWithoutPageConfigInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutPageConfigInputObjectSchema)])
}).strict() as SchemaType;
