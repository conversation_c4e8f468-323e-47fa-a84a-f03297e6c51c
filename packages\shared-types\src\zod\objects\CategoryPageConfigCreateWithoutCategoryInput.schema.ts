/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { JsonNullValueInputSchema } from '../enums/JsonNullValueInput.schema';
import { NullableJsonNullValueInputSchema } from '../enums/NullableJsonNullValueInput.schema';

import type { Prisma } from '@prisma/client';


const literalSchema = z.union([z.string(), z.number(), z.boolean()]);
const jsonSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
    z.union([literalSchema, z.array(jsonSchema.nullable()), z.record(jsonSchema.nullable())])
);

type SchemaType = z.ZodType<Prisma.CategoryPageConfigCreateWithoutCategoryInput>;
export const CategoryPageConfigCreateWithoutCategoryInputObjectSchema: SchemaType = z.object({
    listTemplateSlug: z.string(), detailTemplateSlug: z.string(), listConfig: z.union([z.lazy(() => JsonNullValueInputSchema),
        jsonSchema]), detailConfig: z.union([z.lazy(() => JsonNullValueInputSchema),
            jsonSchema]), variables: z.union([z.lazy(() => JsonNullValueInputSchema),
                jsonSchema]), textTemplates: z.union([z.lazy(() => JsonNullValueInputSchema),
                    jsonSchema]), seo: z.union([z.lazy(() => NullableJsonNullValueInputSchema),
                        jsonSchema]).optional(), enabled: z.boolean().optional().optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional()
}).strict() as SchemaType;
