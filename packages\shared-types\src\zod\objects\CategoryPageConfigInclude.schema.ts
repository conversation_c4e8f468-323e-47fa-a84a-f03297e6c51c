/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryDefaultArgsObjectSchema } from './PartCategoryDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigInclude>;
export const CategoryPageConfigIncludeObjectSchema: SchemaType = z.object({
    category: z.union([z.boolean(),
    z.lazy(() => PartCategoryDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
