/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { TemplateAreaSchema } from '../enums/TemplateArea.schema';
const baseSchema = z.object({
    id: z.number(),
    area: TemplateAreaSchema,
    slug: z.string(),
    title: z.string(),
    description: z.string().nullish(),
    defaultConfig: z.any(),
    enabled: z.boolean().default(true),
    createdAt: z.coerce.date().default(() => new Date()),
    updatedAt: z.coerce.date(),
}
).strict();

/**
 * `PageTemplate` schema excluding foreign keys and relations.
 */
export const PageTemplateScalarSchema = baseSchema;


/**
 * `PageTemplate` schema including all fields (scalar, foreign key, and relations) and validations.
 */
export const PageTemplateSchema = PageTemplateScalarSchema;


/**
 * Schema used for validating Prisma create input. For internal use only.
 * @private
 */
export const PageTemplatePrismaCreateSchema = baseSchema.partial().passthrough();


/**
 * Schema used for validating Prisma update input. For internal use only.
 * @private
 */
export const PageTemplatePrismaUpdateSchema = z.object({
    id: z.union([z.number(), z.record(z.unknown())]),
    area: TemplateAreaSchema,
    slug: z.string(),
    title: z.string(),
    description: z.string().nullish(),
    defaultConfig: z.any(),
    enabled: z.boolean().default(true),
    createdAt: z.coerce.date().default(() => new Date()),
    updatedAt: z.coerce.date()
}).partial().passthrough();


/**
 * `PageTemplate` schema for create operations excluding foreign keys and relations.
 */
export const PageTemplateCreateScalarSchema = baseSchema.partial({
    id: true, enabled: true, createdAt: true, updatedAt: true
});


/**
 * `PageTemplate` schema for create operations including scalar fields, foreign key fields, and validations.
 */
export const PageTemplateCreateSchema = baseSchema.partial({
    id: true, enabled: true, createdAt: true, updatedAt: true
});


/**
 * `PageTemplate` schema for update operations excluding foreign keys and relations.
 */
export const PageTemplateUpdateScalarSchema = baseSchema.partial();


/**
 * `PageTemplate` schema for update operations including scalar fields, foreign key fields, and validations.
 */
export const PageTemplateUpdateSchema = PageTemplateUpdateScalarSchema;

