/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryCreateNestedOneWithoutChildrenInputObjectSchema } from './PartCategoryCreateNestedOneWithoutChildrenInput.schema';
import { PartCategoryCreateNestedManyWithoutParentInputObjectSchema } from './PartCategoryCreateNestedManyWithoutParentInput.schema';
import { PartCreateNestedManyWithoutPartCategoryInputObjectSchema } from './PartCreateNestedManyWithoutPartCategoryInput.schema';
import { CategoryPageConfigCreateNestedOneWithoutCategoryInputObjectSchema } from './CategoryPageConfigCreateNestedOneWithoutCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCreateWithoutImageInput>;
export const PartCategoryCreateWithoutImageInputObjectSchema: SchemaType = z.object({
    name: z.string(), slug: z.string(), description: z.union([z.string(),
    z.null()]).optional().nullable(), level: z.number().optional().optional(), path: z.string(), icon: z.union([z.string(),
    z.null()]).optional().nullable(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), parent: z.lazy(() => PartCategoryCreateNestedOneWithoutChildrenInputObjectSchema).optional().optional(), children: z.lazy(() => PartCategoryCreateNestedManyWithoutParentInputObjectSchema).optional().optional(), parts: z.lazy(() => PartCreateNestedManyWithoutPartCategoryInputObjectSchema).optional().optional(), pageConfig: z.lazy(() => CategoryPageConfigCreateNestedOneWithoutCategoryInputObjectSchema).optional().optional()
}).strict() as SchemaType;
