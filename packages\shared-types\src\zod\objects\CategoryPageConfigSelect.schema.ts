/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryDefaultArgsObjectSchema } from './PartCategoryDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigSelect>;
export const CategoryPageConfigSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), category: z.union([z.boolean(),
    z.lazy(() => PartCategoryDefaultArgsObjectSchema)]).optional(), categoryId: z.boolean().optional().optional(), listTemplateSlug: z.boolean().optional().optional(), detailTemplateSlug: z.boolean().optional().optional(), listConfig: z.boolean().optional().optional(), detailConfig: z.boolean().optional().optional(), variables: z.boolean().optional().optional(), textTemplates: z.boolean().optional().optional(), seo: z.boolean().optional().optional(), enabled: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional()
}).strict() as SchemaType;
