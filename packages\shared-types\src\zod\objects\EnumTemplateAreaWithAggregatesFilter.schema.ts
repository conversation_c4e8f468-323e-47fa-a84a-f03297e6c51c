/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { TemplateAreaSchema } from '../enums/TemplateArea.schema';
import { NestedEnumTemplateAreaWithAggregatesFilterObjectSchema } from './NestedEnumTemplateAreaWithAggregatesFilter.schema';
import { NestedIntFilterObjectSchema } from './NestedIntFilter.schema';
import { NestedEnumTemplateAreaFilterObjectSchema } from './NestedEnumTemplateAreaFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EnumTemplateAreaWithAggregatesFilter>;
export const EnumTemplateAreaWithAggregatesFilterObjectSchema: SchemaType = z.object({
    equals: z.lazy(() => TemplateAreaSchema).optional().optional(), in: z.lazy(() => TemplateAreaSchema).array().optional().optional(), notIn: z.lazy(() => TemplateAreaSchema).array().optional().optional(), not: z.union([z.lazy(() => TemplateAreaSchema),
    z.lazy(() => NestedEnumTemplateAreaWithAggregatesFilterObjectSchema)]).optional(), _count: z.lazy(() => NestedIntFilterObjectSchema).optional().optional(), _min: z.lazy(() => NestedEnumTemplateAreaFilterObjectSchema).optional().optional(), _max: z.lazy(() => NestedEnumTemplateAreaFilterObjectSchema).optional().optional()
}).strict() as SchemaType;
