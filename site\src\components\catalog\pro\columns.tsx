"use client"

import { type ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { CheckCircle2, XCircle, AlertCircle, HelpCircle, Paperclip } from "lucide-react"
import type { Part } from "@/lib/types"
import { MediaThumbnail } from "./MediaThumbnail"

type Applicability = Part["applicabilities"][0]
type CatalogItem = Applicability["catalogItem"]
type PartAttribute = Part["attributes"][0]

const StatusIcon = ({ status }: { status: string }) => {
  switch (status) {
    case 'exact': return <CheckCircle2 className="h-4 w-4 text-green-500" />;
    case 'near': return <CheckCircle2 className="h-4 w-4 text-blue-500" />;
    case 'legacy': return <CheckCircle2 className="h-4 w-4 text-purple-500" />;
    case 'mismatch': return <XCircle className="h-4 w-4 text-red-500" />;
    case 'missing': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    default: return <HelpCircle className="h-4 w-4 text-gray-400" />;
  }
}

const compareValues = (partAttr: PartAttribute, itemAttr: CatalogItem["attributes"][0] | undefined) => {
    if (!itemAttr) return { status: 'missing', notes: 'Атрибут отсутствует' };
    const template = partAttr.template;
    const partValue = partAttr.value;
    const itemValue = itemAttr.value;
  
    if (template.dataType === 'NUMBER') {
      const pVal = parseFloat(partValue);
      const iVal = parseFloat(itemValue);
      const tolerance = template.tolerance ?? 0;
      if (Math.abs(pVal - iVal) <= tolerance) {
        return { status: 'exact', notes: pVal === iVal ? 'Точное совпадение' : `Совпадение в пределах допуска (±${tolerance}${template.unit || ''})` };
      }
      return { status: 'mismatch', notes: `Отклонение: ${iVal - pVal > 0 ? '+' : ''}${(iVal - pVal).toFixed(3)} ${template.unit || ''}` };
    } else {
      if (partValue.toLowerCase() === itemValue.toLowerCase()) {
        return { status: 'exact', notes: 'Точное совпадение' };
      }
      if (template.synonymGroups && template.synonymGroups.length > 0) {
        for (const group of template.synonymGroups) {
          const canonicalMatch = group.canonicalValue?.toLowerCase() === itemValue.toLowerCase();
          const synonymMatch = group.synonyms.some((s: { value: string }) => s.value.toLowerCase() === itemValue.toLowerCase());
          if (canonicalMatch || synonymMatch) {
             let status: 'exact' | 'near' | 'legacy' = 'exact';
             if (group.compatibilityLevel === 'NEAR') status = 'near';
             if (group.compatibilityLevel === 'LEGACY') status = 'legacy';
             return { status, notes: group.notes || `Совпадение по группе синонимов "${group.name}"` };
          }
        }
      }
      return { status: 'mismatch', notes: 'Значения не совпадают' };
    }
  };
  

export const createColumns = (partAttributes: PartAttribute[]): ColumnDef<Applicability>[] => {
  const columns: ColumnDef<Applicability>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "catalogItem.sku",
      header: "SKU / Бренд",
      cell: ({ row }) => {
        const item = row.original.catalogItem;
        return (
          <div>
            <div className="font-bold">{item.sku}</div>
            <div className="text-xs text-muted-foreground">{item.brand.name}</div>
          </div>
        )
      },
    },
    ...partAttributes.map((partAttr): ColumnDef<Applicability> => ({
      id: `attr-${partAttr.templateId}`,
      header: () => (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className="cursor-help">{partAttr.template.title}</span>
            </TooltipTrigger>
            <TooltipContent>
              <p>Эталон: {partAttr.value} {partAttr.template.unit}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ),
      cell: ({ row }) => {
        const itemAttr = row.original.catalogItem.attributes.find((a: { templateId: number }) => a.templateId === partAttr.templateId);
        const comparison = compareValues(partAttr, itemAttr);
        return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center justify-center gap-2">
                    <StatusIcon status={comparison.status} />
                    <span>{itemAttr?.value || '-'}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>{comparison.notes}</TooltipContent>
              </Tooltip>
            </TooltipProvider>
        );
      },
    })),
    {
      accessorKey: "accuracy",
      header: "Точность",
      cell: ({ row }) => <Badge variant={row.original.accuracy === 'EXACT_MATCH' ? 'default' : 'secondary'}>{row.original.accuracy}</Badge>
    },
    {
        id: "media",
        header: "",
        cell: ({ row }) => {
            const item = row.original.catalogItem;
            if (item.mediaAssets.length === 0) return null;
            return (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Paperclip className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Доп. медиа: {item.mediaAssets.length}</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            )
        }
    }
  ];

  return columns;
};
