/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { JsonNullValueInputSchema } from '../enums/JsonNullValueInput.schema';
import { NullableJsonNullValueInputSchema } from '../enums/NullableJsonNullValueInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';

import type { Prisma } from '@prisma/client';


const literalSchema = z.union([z.string(), z.number(), z.boolean()]);
const jsonSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
    z.union([literalSchema, z.array(jsonSchema.nullable()), z.record(jsonSchema.nullable())])
);

type SchemaType = z.ZodType<Prisma.CategoryPageConfigUpdateWithoutCategoryInput>;
export const CategoryPageConfigUpdateWithoutCategoryInputObjectSchema: SchemaType = z.object({
    listTemplateSlug: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), detailTemplateSlug: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), listConfig: z.union([z.lazy(() => JsonNullValueInputSchema),
        jsonSchema]).optional(), detailConfig: z.union([z.lazy(() => JsonNullValueInputSchema),
            jsonSchema]).optional(), variables: z.union([z.lazy(() => JsonNullValueInputSchema),
                jsonSchema]).optional(), textTemplates: z.union([z.lazy(() => JsonNullValueInputSchema),
                    jsonSchema]).optional(), seo: z.union([z.lazy(() => NullableJsonNullValueInputSchema),
                        jsonSchema]).optional(), enabled: z.union([z.boolean(),
                        z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(), createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
                        z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
                        z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional()
}).strict() as SchemaType;
