/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
const baseSchema = z.object({
    id: z.number(),
    listTemplateSlug: z.string(),
    detailTemplateSlug: z.string(),
    listConfig: z.any(),
    detailConfig: z.any(),
    variables: z.any(),
    textTemplates: z.any(),
    seo: z.any().nullish(),
    enabled: z.boolean().default(true),
    createdAt: z.coerce.date().default(() => new Date()),
    updatedAt: z.coerce.date(),
}
).strict();
const relationSchema = z.object({
    category: z.record(z.unknown()),
}
);
const fkSchema = z.object({
    categoryId: z.number(),
}
);

/**
 * `CategoryPageConfig` schema excluding foreign keys and relations.
 */
export const CategoryPageConfigScalarSchema = baseSchema;


/**
 * `CategoryPageConfig` schema including all fields (scalar, foreign key, and relations) and validations.
 */
export const CategoryPageConfigSchema = CategoryPageConfigScalarSchema.merge(fkSchema).merge(relationSchema.partial());


/**
 * Schema used for validating Prisma create input. For internal use only.
 * @private
 */
export const CategoryPageConfigPrismaCreateSchema = baseSchema.partial().passthrough();


/**
 * Schema used for validating Prisma update input. For internal use only.
 * @private
 */
export const CategoryPageConfigPrismaUpdateSchema = z.object({
    id: z.union([z.number(), z.record(z.unknown())]),
    listTemplateSlug: z.string(),
    detailTemplateSlug: z.string(),
    listConfig: z.any(),
    detailConfig: z.any(),
    variables: z.any(),
    textTemplates: z.any(),
    seo: z.any().nullish(),
    enabled: z.boolean().default(true),
    createdAt: z.coerce.date().default(() => new Date()),
    updatedAt: z.coerce.date()
}).partial().passthrough();


/**
 * `CategoryPageConfig` schema for create operations excluding foreign keys and relations.
 */
export const CategoryPageConfigCreateScalarSchema = baseSchema.partial({
    id: true, enabled: true, createdAt: true, updatedAt: true
});


/**
 * `CategoryPageConfig` schema for create operations including scalar fields, foreign key fields, and validations.
 */
export const CategoryPageConfigCreateSchema = CategoryPageConfigCreateScalarSchema.merge(fkSchema);


/**
 * `CategoryPageConfig` schema for update operations excluding foreign keys and relations.
 */
export const CategoryPageConfigUpdateScalarSchema = baseSchema.partial();


/**
 * `CategoryPageConfig` schema for update operations including scalar fields, foreign key fields, and validations.
 */
export const CategoryPageConfigUpdateSchema = CategoryPageConfigUpdateScalarSchema.merge(fkSchema.partial());

