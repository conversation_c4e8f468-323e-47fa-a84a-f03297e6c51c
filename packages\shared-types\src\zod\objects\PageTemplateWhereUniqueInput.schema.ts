/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PageTemplateWhereInputObjectSchema } from './PageTemplateWhereInput.schema';
import { EnumTemplateAreaFilterObjectSchema } from './EnumTemplateAreaFilter.schema';
import { TemplateAreaSchema } from '../enums/TemplateArea.schema';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { JsonFilterObjectSchema } from './JsonFilter.schema';
import { BoolFilterObjectSchema } from './BoolFilter.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PageTemplateWhereUniqueInput>;
export const PageTemplateWhereUniqueInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), slug: z.string().optional().optional(), AND: z.union([z.lazy(() => PageTemplateWhereInputObjectSchema),
    z.lazy(() => PageTemplateWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => PageTemplateWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => PageTemplateWhereInputObjectSchema),
    z.lazy(() => PageTemplateWhereInputObjectSchema).array()]).optional(), area: z.union([z.lazy(() => EnumTemplateAreaFilterObjectSchema),
    z.lazy(() => TemplateAreaSchema)]).optional(), title: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), description: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), defaultConfig: z.lazy(() => JsonFilterObjectSchema).optional().optional(), enabled: z.union([z.lazy(() => BoolFilterObjectSchema),
    z.boolean()]).optional(), createdAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional()
}).strict() as SchemaType;
