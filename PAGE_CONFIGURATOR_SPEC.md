### Конструктор страниц каталога (Category Page Configurator)

#### Цели
- Типобезопасный конфиг страниц для каждой `PartCategory` без дублирования компонентов.
- Шаблоны для листинга и карточки товара/детали, а также текстовые шаблоны (H1/Title/Meta) с подстановкой значений из CORE/ATTRIBUTE.
- Простое расширение: новые шаблоны добавляются на стороне фронтенда, БД хранит только выбранные слаги и конфиги.

#### Встраивание в текущую архитектуру
- Данные и доменная модель уже есть в `@catalog_schema.zmodel` (Part/PartAttribute, CatalogItem/CatalogItemAttribute, Brand, PartCategory).
- Добавляем per-category конфиг `CategoryPageConfig` (1:1 с `PartCategory`).
- Не добавляем новые страницы — интеграция в существующие: `site/src/pages/catalog/categories/[slug].astro` и `site/src/pages/catalog/items/[id].astro`.
- Админка: добавляем вкладку конфигуратора в диалог/страницу редактирования категории.

---

### Модель данных (ZenStack ZModel)

- enum ListEntity { PART ITEM }
- model CategoryPageConfig
  - id Int @id @default(autoincrement())
  - category PartCategory @relation(fields: [categoryId], references: [id])
  - categoryId Int @unique
  - listEntity ListEntity @default(ITEM)
  - listTemplateSlug String // напр. "grid-basic" | "table-compact"
  - listConfig Json // валидируем Zod-схемой
  - detailTemplateSlug String // напр. "spec-two-column" | "tabs"
  - detailConfig Json // валидируем Zod-схемой
  - variables Json // описание переменных (CORE/ATTRIBUTE/COMPUTED)
  - textTemplates Json // { list: {...}, detail: {...} }
  - seo Json? // расширяемые SEO-настройки
  - enabled Boolean @default(true)
  - timestamps: createdAt/updatedAt
  - @@allow('read', true)
  - @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')

Примечания:
- Справочник шаблонов не нужен в БД: вместо `PageTemplate` используем `*TemplateSlug` + проверку через Zod на стороне API/UI.
- Наследование: если нет записи для категории — используем дефолт (глобальная константа). В следующих итерациях можно добавить наследование от родителя.

---

### Типы и валидация (packages/shared-types)

- Zod-схемы:
  - CategoryListConfigSchema: { layout: 'grid-basic' | 'table-compact', columns?: 2|3|4, pageSize: number, filters: number[], sortOptions: string[] }
  - ProductDetailConfigSchema: { layout: 'spec-two-column' | 'tabs', blocks: string[], attributeSections: Array<{ title: string, groupId?: number, templateIds?: number[] }>, badges?: string[] }
  - VariableSchema: CORE | ATTRIBUTE | COMPUTED c обязательными полями и форматированием.
  - TextTemplatesSchema: { list: { h1,title,metaDescription? }, detail: { h1,title,metaDescription? } }
  - CategoryPageConfigInputSchema: агрегирует поля выше + *TemplateSlug как z.enum([...])

- Экспортировать типы (infer) для использования в API, site, cpanel.

---

### Рендер текстовых шаблонов

- Источники данных (контекст):
  - CORE: item.sku, brand.name, part.name, category.name/slug
  - ATTRIBUTE: по templateId из `CatalogItemAttribute` и/или `PartAttribute`; для чисел используем `numericValue` + форматирование, для строк — `raw|canonical`.
  - COMPUTED: pattern из уже вычисленных переменных.
- Синтаксис в шаблонах: `{var}`, условная вставка `{var?}`, фолбэк `{var|default:"N/A"}`.
- Форматтеры: `|num:decimals` (с опциями showUnit/trimZeros), `|upper`, `|lower` (минимум MVP: num + канонизация строк по флагу в VariableSchema).
- Алгоритм:
  1) Сбор источников (item/part/brand/category + их атрибуты по templateId)
  2) Вычисление переменных: CORE → ATTRIBUTE → COMPUTED
  3) Подстановка в шаблоны с учётом `?`/фолбэков

---

### API (tRPC)

Роут `pageConfig`:
- getByCategorySlug(slug: string): возвращает объединённый конфиг (из БД либо дефолт), плюс список допустимых `*TemplateSlug`.
- upsert(input): только ADMIN. Валидация Zod; сохраняем/обновляем запись по categoryId.

Безопасность: публичное чтение (read allow true), запись только ADMIN.

---

### Site интеграция

- `categories/[slug].astro`:
  - загрузить конфиг через `trpc.site.pageConfig.getByCategorySlug`
  - выбрать компонент листинга по `listTemplateSlug` из реестра
  - выполнить запросы на данные согласно `listEntity` и `listConfig` (переиспользуем `SiteService.searchParts/searchCatalogItems`)
  - отрендерить H1/Title/Meta через рендерер шаблонов

- `items/[id].astro`:
  - найти связанную категорию (через первую применимость либо параметр в URL в будущем)
  - загрузить конфиг категории, выбрать `detailTemplateSlug`
  - создать контекст переменных (item, brand, part, category), отрендерить H1/Title/Meta
  - отрисовать компонент карточки товара

---

### CPanel интеграция

- В `EditCategoryDialog.vue`/расширение: вкладка «Конфигурация».
- Форма генерируется из Zod-схем: select для `listEntity`, enum для `*TemplateSlug`, автогенерируемые формы для `listConfig`/`detailConfig`.
- Редактор переменных: конструктор (CORE/ATTRIBUTE/COMPUTED) + предпросмотр H1/Title/Meta.

---

### Дефолты (сидинг)

- listEntity: ITEM
- listTemplateSlug: 'grid-basic', detailTemplateSlug: 'spec-two-column'
- listConfig: { columns: 3, pageSize: 24, filters: [], sortOptions: ['sku_asc'] }
- detailConfig: { layout: 'spec-two-column', blocks: ['gallery','attributes','applicability','media'] }
- variables: sku, brand, title(part.name), size = computed(id x od x width)
- textTemplates: detail.h1 = "{title} {sku?} {size?}" и т.д.

---

### План реализации (итерации)

1) Схема:
   - Добавить enum ListEntity и model CategoryPageConfig в `api/catalog_schema.zmodel`.
   - `bunx zenstack generate` (обновить Prisma/Zod/TRPC CRUD).

2) Общие типы:
   - Добавить Zod-схемы конфигов в `packages/shared-types/src/zod/page-config.ts`.

3) API:
   - Новый tRPC роутер `api/routers/page-config.ts` (getByCategorySlug, upsert).

4) Site:
   - Реестр шаблонов в `site/src/lib/catalog.ts`.
   - Рендерер текстовых шаблонов в `site/src/lib/utils.ts` (или `catalog.ts`).
   - Интеграция в `site/src/pages/catalog/categories/[slug].astro` и `site/src/pages/catalog/items/[id].astro`.

5) CPanel:
   - Добавить вкладку «Конфигурация» в `EditCategoryDialog.vue`.
   - Формы на Volt UI с авто-генерацией по Zod.

6) Тесты/проверка:
   - Smoke-пути на site для категории и карточки.
   - Валидация API и прав.

---

### Будущее расширение
- Наследование конфигов от родителя/глобальных дефолтов.
- Мультишаблонность (A/B) и версионирование.
- Локализация текстовых шаблонов.
- Доп. форматтеры и канонизация строк через синонимы.
