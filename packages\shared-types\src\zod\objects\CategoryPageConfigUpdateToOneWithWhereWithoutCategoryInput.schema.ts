/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CategoryPageConfigWhereInputObjectSchema } from './CategoryPageConfigWhereInput.schema';
import { CategoryPageConfigUpdateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUpdateWithoutCategoryInput.schema';
import { CategoryPageConfigUncheckedUpdateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUncheckedUpdateWithoutCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigUpdateToOneWithWhereWithoutCategoryInput>;
export const CategoryPageConfigUpdateToOneWithWhereWithoutCategoryInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CategoryPageConfigWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => CategoryPageConfigUpdateWithoutCategoryInputObjectSchema), z.lazy(() => CategoryPageConfigUncheckedUpdateWithoutCategoryInputObjectSchema)])
}).strict() as SchemaType;
