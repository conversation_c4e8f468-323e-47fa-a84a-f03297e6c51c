import { cn } from "@/lib/utils"
import type React from "react"

export function DotBackground({ children, className }: { children?: React.ReactNode; className?: string }) {
  return (
    <div className={cn("relative flex w-full items-center justify-center bg-background", className)}>
      <div
        className={cn(
          "absolute inset-0",
          "[background-size:20px_20px]",
          // Светлая тема: темные точки
          "dark:[background-image:radial-gradient(rgba(255,255,255,0.15)_1px,transparent_1px)]",
          // Темная тема: светлые точки
          "[background-image:radial-gradient(rgba(0,0,0,0.08)_1px,transparent_1px)]",
        )}
      />
      {/* Radial gradient for the container to give a faded look */}
      <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-background [mask-image:radial-gradient(ellipse_at_center,transparent_20%,white)] dark:[mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
      <div className="relative z-20 w-full">{children}</div>
    </div>
  )
}

