"use client"

import { useState } from "react"
import { BarChart3, Grid3X3, List, Package, Database, Layers, TrendingUp, Clock } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard, ModernCardContent } from "@/components/ui/modern-card"
import { Badge } from "@/components/ui/badge"
import { MediaThumbnail } from "../pro/MediaThumbnail"
import { useCatalogSearch } from "../pro/useCatalogSearch"
import { useCatalogMetadata } from "@/hooks/useCatalogData"
import { MobileFiltersDrawer } from "../pro/MobileFiltersDrawer"
import { TrpcBoundary } from "@/components/providers/TrpcBoundary"
import { navigate } from "astro:transitions/client"

export default function ResultsIsland() {
  return (
    <TrpcBoundary>
      <ResultsIslandInner />
    </TrpcBoundary>
  )
}

function ResultsIslandInner() {
  const { results, isLoading, totalCount, clearFilters, filters, setFilters, availableAttributeValues } = useCatalogSearch()
  const { categories, brands, templates } = useCatalogMetadata()
  const [viewMode, setViewMode] = useState<"detailed" | "grid" | "table">("detailed")

  const handlePartClick = (partId: number) => {
    navigate(`/catalog/parts/${partId}`)
  }

  const activeFiltersCount = filters.categoryIds.length +
    filters.brandIds.length +
    Object.keys(filters.attributeFilters || {}).length +
    (filters.isOemOnly ? 1 : 0)

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="container max-w-none p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-bold tracking-tight">Всего: {totalCount}</h2>
          </div>

          <div className="flex items-center gap-2">
            {/* Мобильный фильтр drawer */}
            <MobileFiltersDrawer
              filters={filters}
              setFilters={setFilters}
              activeFiltersCount={activeFiltersCount}
              onClearAll={clearFilters}
              categories={categories}
              brands={brands}
              attributeTemplates={templates}
              availableAttributeValues={availableAttributeValues}
            />

            <div className="flex items-center gap-1 p-1 bg-muted/50 rounded border border-border/40">
              <ModernButton variant={viewMode === "detailed" ? "default" : "ghost"} size="sm" onClick={() => setViewMode("detailed")} className="h-7 w-7 p-0">
                <List className="h-3 w-3" />
              </ModernButton>
              <ModernButton variant={viewMode === "grid" ? "default" : "ghost"} size="sm" onClick={() => setViewMode("grid")} className="h-7 w-7 p-0">
                <Grid3X3 className="h-3 w-3" />
              </ModernButton>
              <ModernButton variant={viewMode === "table" ? "default" : "ghost"} size="sm" onClick={() => setViewMode("table")} className="h-7 w-7 p-0">
                <BarChart3 className="h-3 w-3" />
              </ModernButton>
            </div>
          </div>
        </div>

        {isLoading && (
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <ModernCard key={i} variant="elevated" className="p-4">
                <div className="flex gap-4">
                  <div className="bg-muted/50 rounded animate-pulse h-24 w-24"></div>
                  <div className="flex-1 space-y-3">
                    <div className="h-5 bg-muted/50 rounded animate-pulse w-3/4"></div>
                    <div className="h-4 bg-muted/50 rounded animate-pulse w-1/2"></div>
                    <div className="flex gap-2">
                      <div className="h-5 bg-muted/50 rounded-full animate-pulse w-20"></div>
                      <div className="h-5 bg-muted/50 rounded-full animate-pulse w-24"></div>
                    </div>
                  </div>
                </div>
              </ModernCard>
            ))}
          </div>
        )}

        {!isLoading && results.length === 0 ? (
          <ModernCard variant="elevated" className="text-center py-12 border-2 border-dashed border-border-strong">
            <ModernCardContent>
              <div className="flex flex-col items-center gap-3">
                <div className="h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center">
                  <Database className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-1">Группы не найдены</h3>
                  <p className="text-muted-foreground text-sm max-w-md">Попробуйте изменить критерии поиска или сбросить фильтры.</p>
                </div>
                <ModernButton variant="outline" onClick={clearFilters} size="sm">
                  Очистить фильтры
                </ModernButton>
              </div>
            </ModernCardContent>
          </ModernCard>
        ) : (
          <div className="space-y-3 animate-fade-in">
            {results?.map((part: any, index: number) => (
              <ModernCard
                key={part.id}
                variant="elevated"
                className="group hover:shadow-strong transition-all duration-200 animate-slide-up border hover:border-primary/20 cursor-pointer"
                style={{ animationDelay: `${index * 30}ms` }}
                onClick={() => handlePartClick(part.id)}
              >
                <ModernCardContent className="p-4">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex-shrink-0 flex justify-center sm:justify-start">
                      <MediaThumbnail mediaAsset={part.image ?? undefined} size="md" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-bold text-primary tracking-tight mb-2" title={part.name ?? ''}>
                            {part.name}
                          </h3>
                          <div className="flex flex-wrap items-center gap-2 mb-3">
                            <Badge variant="outline" className="text-xs">
                              {part.partCategory?.name}
                            </Badge>
                            <Badge variant="secondary" className="font-mono text-xs">
                              ID: {part.id}
                            </Badge>
                            <Badge variant="default" className="gap-1.5 pl-2">
                              <Package className="h-3 w-3" />
                              {part.applicabilitiesCount} аналогов
                            </Badge>
                          </div>
                          <div className="flex flex-wrap gap-1 mb-3">
                            {part.attributes.slice(0, 3).map((attr: any) => (
                                <div key={attr.template.title} className="inline-flex items-center gap-1.5 px-2 py-0.5 rounded-full bg-muted border text-xs">
                                    <span className="text-muted-foreground">{attr.template.title}:</span>
                                    <span className="font-semibold">{attr.value} {attr.template.unit}</span>
                                </div>
                            ))}
                            {part.attributes.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{part.attributes.length - 3} параметров
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="border-t border-border mt-3 pt-3 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>Обновлено: {new Date(part.updatedAt).toLocaleDateString("ru-RU")}</span>
                        </div>

                        <div className="flex items-center gap-2">
                          <ModernButton variant="gradient" size="sm" className="gap-1 h-7 px-3 text-xs w-full sm:w-auto">
                            <Layers className="h-3 w-3" />
                            Посмотреть группу
                          </ModernButton>
                        </div>
                      </div>
                    </div>
                  </div>
                </ModernCardContent>
              </ModernCard>
            ))}
          </div>
        )}

      </div>
    </div>
  )
}
