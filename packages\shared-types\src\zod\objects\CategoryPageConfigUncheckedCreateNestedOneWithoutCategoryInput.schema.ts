/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CategoryPageConfigCreateWithoutCategoryInputObjectSchema } from './CategoryPageConfigCreateWithoutCategoryInput.schema';
import { CategoryPageConfigUncheckedCreateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUncheckedCreateWithoutCategoryInput.schema';
import { CategoryPageConfigCreateOrConnectWithoutCategoryInputObjectSchema } from './CategoryPageConfigCreateOrConnectWithoutCategoryInput.schema';
import { CategoryPageConfigWhereUniqueInputObjectSchema } from './CategoryPageConfigWhereUniqueInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigUncheckedCreateNestedOneWithoutCategoryInput>;
export const CategoryPageConfigUncheckedCreateNestedOneWithoutCategoryInputObjectSchema: SchemaType = z.object({
    create: z.union([z.lazy(() => CategoryPageConfigCreateWithoutCategoryInputObjectSchema), z.lazy(() => CategoryPageConfigUncheckedCreateWithoutCategoryInputObjectSchema)]).optional(), connectOrCreate: z.lazy(() => CategoryPageConfigCreateOrConnectWithoutCategoryInputObjectSchema).optional().optional(), connect: z.lazy(() => CategoryPageConfigWhereUniqueInputObjectSchema).optional().optional()
}).strict() as SchemaType;
