/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryDefaultArgsObjectSchema } from './PartCategoryDefaultArgs.schema';
import { PartCategoryInputSchema } from '../input/PartCategoryInput.schema';
import { PartInputSchema } from '../input/PartInput.schema';
import { CategoryPageConfigDefaultArgsObjectSchema } from './CategoryPageConfigDefaultArgs.schema';
import { MediaAssetDefaultArgsObjectSchema } from './MediaAssetDefaultArgs.schema';
import { PartCategoryCountOutputTypeDefaultArgsObjectSchema } from './PartCategoryCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategorySelect>;
export const PartCategorySelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), name: z.boolean().optional().optional(), slug: z.boolean().optional().optional(), description: z.boolean().optional().optional(), parent: z.union([z.boolean(),
    z.lazy(() => PartCategoryDefaultArgsObjectSchema)]).optional(), parentId: z.boolean().optional().optional(), children: z.union([z.boolean(),
    z.lazy(() => PartCategoryInputSchema.findMany)]).optional(), level: z.boolean().optional().optional(), path: z.boolean().optional().optional(), icon: z.boolean().optional().optional(), parts: z.union([z.boolean(),
    z.lazy(() => PartInputSchema.findMany)]).optional(), pageConfig: z.union([z.boolean(),
    z.lazy(() => CategoryPageConfigDefaultArgsObjectSchema)]).optional(), imageId: z.boolean().optional().optional(), image: z.union([z.boolean(),
    z.lazy(() => MediaAssetDefaultArgsObjectSchema)]).optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), _count: z.union([z.boolean(),
    z.lazy(() => PartCategoryCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
