"use client";

import { Badge } from "@/components/ui/badge";
import { ModernButton } from "@/components/ui/modern-button";
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card";
import { MediaThumbnail } from "../pro/MediaThumbnail";
import { Layers, Building, Wrench, CheckCircle2 } from "lucide-react";
import { navigate } from "astro:transitions/client";
import type { Part } from "@/lib/types";

type CatalogItem = Part["applicabilities"][0]["catalogItem"];
type Applicability = Part["applicabilities"][0];

export default function CatalogItemIsland({ item }: { item: CatalogItem & { applicabilities: Applicability[] } }) {

  const handlePartClick = (partId: number) => {
    navigate(`/catalog/parts/${partId}`)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <ModernCard>
        <ModernCardHeader className="flex flex-row items-start justify-between">
          <div>
            <div className="flex items-center gap-4">
               <MediaThumbnail mediaAsset={item.image} size="lg" />
               <div>
                 <ModernCardTitle className="text-3xl font-bold font-mono">{item.sku}</ModernCardTitle>
                 <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline"><Building className="h-3 w-3 mr-1.5" />{item.brand.name}</Badge>
                    {item.brand.isOem && <Badge variant="secondary">OEM</Badge>}
                 </div>
               </div>
            </div>
            <p className="text-muted-foreground mt-4 max-w-2xl">{item.description}</p>
          </div>
           <a href="/catalog" className="text-sm text-muted-foreground hover:text-primary transition-colors">← К поиску</a>
        </ModernCardHeader>
        <ModernCardContent className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-1">
            <h3 className="font-bold text-lg mb-4">Характеристики</h3>
            <div className="space-y-2">
              {item.attributes.map((attr) => (
                <div key={attr.id} className="flex justify-between text-sm p-2 rounded bg-muted/50">
                  <span className="text-muted-foreground">{attr.template.title}</span>
                  <span className="font-semibold">{attr.value} {attr.template.unit}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="md:col-span-2">
            <h3 className="font-bold text-lg mb-4">Входит в группы взаимозаменяемости</h3>
            {item.applicabilities.length > 0 ? (
                 <div className="space-y-3">
                    {item.applicabilities.map((app) => (
                        <ModernCard 
                            key={app.id} 
                            variant="outline" 
                            className="hover:border-primary/40 transition-colors cursor-pointer"
                            onClick={() => handlePartClick(app.part.id)}
                        >
                            <ModernCardContent className="p-4 flex items-center justify-between">
                                <div>
                                    <h4 className="font-semibold text-primary">{app.part.name}</h4>
                                    <p className="text-sm text-muted-foreground">{app.part.partCategory.name}</p>
                                </div>
                                <div className="flex items-center gap-4">
                                   <Badge variant={app.accuracy === 'EXACT_MATCH' ? 'default' : 'secondary'} className="gap-1.5">
                                        <CheckCircle2 className="h-3 w-3"/>
                                        {app.accuracy}
                                   </Badge>
                                   <ModernButton variant="outline" size="sm" className="gap-1.5 h-7 text-xs">
                                     <Layers className="h-3 w-3" />
                                     Перейти к группе
                                   </ModernButton>
                                </div>
                            </ModernCardContent>
                        </ModernCard>
                    ))}
                 </div>
            ) : (
                <div className="text-center py-12 border-2 border-dashed rounded-lg">
                    <Wrench className="h-8 w-8 text-muted-foreground mx-auto mb-2"/>
                    <h4 className="font-semibold">Не состоит в группах</h4>
                    <p className="text-sm text-muted-foreground">Этот артикул еще не был сопоставлен с эталонной группой.</p>
                </div>
            )}
          </div>
        </ModernCardContent>
      </ModernCard>
    </div>
  );
}

