"use client"

import { useMemo } from "react"
import { trpc } from "@/lib/trpc"
import { useCatalogGlobalState } from "@/lib/catalog-state"
import { useDebounce } from "@/hooks/useDebounce"

// Хук для использования в отдельных компонентах
export function useCatalogSearch() {
  const { filters, setFilters, updateFilters, clearFilters } = useCatalogGlobalState()

  // Дебаунсинг для поискового запроса
  const debouncedQuery = useDebounce(filters.query, 300)

  // Загружаем данные через изолированный site API
  const { data: searchResult, isLoading, error } = trpc.site.search.parts.useQuery({
    search: debouncedQuery || undefined,
    brandIds: filters.brandIds,
    categoryIds: filters.categoryIds,
    isOemOnly: filters.isOemOnly,
    attributeFilters: Object.entries(filters.attributeFilters || {}).map(([templateId, f]) => ({
      templateId: Number(templateId),
      values: f.values?.length ? f.values : undefined,
      minValue: f.numericRange?.[0],
      maxValue: f.numericRange?.[1],
    })),
    limit: 50,
    offset: 0,
    sortBy: 'updatedAt',
    sortDir: 'desc',
  }, {
    staleTime: 30_000, // 30 секунд для результатов поиска
    gcTime: 5 * 60_000, // 5 минут в кэше
  })

  // Логирование для отладки
  if (typeof window !== 'undefined') {
    console.log('Search filters:', filters)
    console.log('Search result:', searchResult)
    console.log('Search error:', error)
    console.log('Is loading:', isLoading)
  }

  // TODO: когда будем делать фильтры по атрибутам, понадобится логика для агрегации
  const availableAttributeValues = useMemo(() => {
    return { values: {}, numericStats: {} }
  }, [searchResult])

  return {
    filters,
    setFilters,
    updateFilters,
    clearFilters,
    results: searchResult?.items ?? [],
    totalCount: searchResult?.total || 0,
    filteredCount: searchResult?.items.length || 0,
    isLoading,
    availableAttributeValues,
  }
}

