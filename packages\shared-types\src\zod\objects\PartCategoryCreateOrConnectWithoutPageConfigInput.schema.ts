/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryCreateWithoutPageConfigInputObjectSchema } from './PartCategoryCreateWithoutPageConfigInput.schema';
import { PartCategoryUncheckedCreateWithoutPageConfigInputObjectSchema } from './PartCategoryUncheckedCreateWithoutPageConfigInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCreateOrConnectWithoutPageConfigInput>;
export const PartCategoryCreateOrConnectWithoutPageConfigInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCategoryCreateWithoutPageConfigInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutPageConfigInputObjectSchema)])
}).strict() as SchemaType;
