"use client"

import { motion } from "motion/react"
import { Badge } from "@/components/ui/badge"
import { CheckCir<PERSON>, Info, Setting<PERSON>, Target } from "lucide-react"

type RadialSealSpecs = {
  dimensions: string
  material: string
  temperature: string
  speed?: string
  standard?: string
}

type Example = {
  title: string
  original: string
  originalSpecs: Record<string, string>
  compatible: Array<{ part: string; match: string; price: string; availability: string; specs: string }>
  savings: string
  description: string
  technicalNote?: string
}

const examplesData: readonly Example[] = [
  {
    title: "Сальники радиальные",
    original: "Corteco 12345-ABC",
    originalSpecs: {
      dimensions: "25×47×7mm",
      material: "NBR 70 Shore A",
      temperature: "-40°C до +120°C",
      speed: "до 15 м/с",
      standard: "DIN 3760",
    } satisfies RadialSealSpecs as Record<string, string>,
    compatible: [
      { part: "SKF 789-XYZ", match: "98%", price: "-35%", availability: "В наличии", specs: "25×47×7mm, NBR 70 Shore A, -40°C/+120°C" },
      { part: "Febi 456-DEF", match: "96%", price: "-28%", availability: "2-3 дня", specs: "25×47×7mm, NBR 72 Shore A, -35°C/+125°C" },
      { part: "NOK 321-GHI", match: "99%", price: "-42%", availability: "Под заказ", specs: "25×47×7mm, NBR 70 Shore A, -40°C/+120°C" },
    ],
    savings: "до 42%",
    description:
      "Радиальные сальники для валов с идентичными размерами и материалами. Все аналоги соответствуют стандарту DIN 3760 и имеют подтвержденную взаимозаменяемость.",
    technicalNote:
      "Различия в твердости материала (±2 Shore A) не влияют на эксплуатационные характеристики при стандартных условиях работы.",
  },
]

export function Examples() {
  return (
    <div className="relative container mx-auto px-4">
      <div className="text-center mb-12">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="inline-flex items-center gap-2 bg-secondary border text-chart-2 px-4 py-2 rounded-full text-base font-medium mb-3"
        >
          <CheckCircle className="w-4 h-4" />
          Реальные кейсы
        </motion.div>
        <h2 className="text-5xl lg:text-5xl font-bold mb-3">
          <span className="text-muted-foreground">Примеры взаимозаменяемости</span>{" "}
          <span className="text-chart-2">деталей</span>
        </h2>
        <p className="text-2xl text-muted-foreground">
          Реальные кейсы поиска аналогов с техническими характеристиками и экономическим эффектом
        </p>
      </div>

      <div className="space-y-16">
        {examplesData.map((example, index) => (
          <motion.div key={index} initial={{ opacity: 0, y: 60 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.8, delay: index * 0.2 }} viewport={{ once: true }}>
            <div className="bg-card backdrop-blur-xl border rounded-2xl overflow-hidden">
              <div className="bg-secondary p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-background rounded-xl flex items-center justify-center">
                      <Settings className="w-6 h-6 text-foreground" />
                    </div>
                    <div>
                      <h3 className="text-3xl font-bold text-card-foreground">{example.title}</h3>
                      <p className="text-muted-foreground">{example.description}</p>
                    </div>
                  </div>
                  <Badge className="bg-chart-2/20 text-chart-2 border-chart-2/30 border px-4 py-2 text-lg">Экономия {example.savings}</Badge>
                </div>
              </div>

              <div className="grid lg:grid-cols-2 gap-0">
                {/* Original Part */}
                <div className="p-6 bg-card backdrop-blur-xl border-r">
                  <h4 className="font-semibold text-card-foreground mb-4 flex items-center gap-3">
                    <div className="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                      <Target className="w-4 h-4 text-secondary-foreground" />
                    </div>
                    Оригинальная деталь
                  </h4>

                  <div className="bg-secondary backdrop-blur-xl border rounded-xl p-6 mb-6">
                    <div className="font-mono text-2xl font-bold text-card-foreground mb-4">{example.original}</div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      {Object.entries(example.originalSpecs).map(([key, value]) => (
                        <div key={key} className="bg-background rounded-lg p-3">
                          <div className="text-muted-foreground capitalize text-sm mb-1">{key}:</div>
                          <div className="font-medium text-foreground">{value}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {example.technicalNote && (
                    <div className="bg-secondary border rounded-xl p-4 backdrop-blur-xl">
                      <div className="flex items-start gap-3">
                        <Info className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                        <div className="text-sm">
                          <div className="font-medium text-primary mb-2">Техническое примечание:</div>
                          <div className="text-muted-foreground">{example.technicalNote}</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Compatible Parts */}
                <div className="p-6 bg-card backdrop-blur-xl">
                  <h4 className="font-semibold text-card-foreground mb-4 flex items-center gap-3">
                    <div className="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-secondary-foreground" />
                    </div>
                    Совместимые аналоги
                  </h4>

                  <div className="space-y-4">
                    {example.compatible.map((part, i) => (
                      <motion.div key={i} initial={{ opacity: 0, x: 20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.5, delay: i * 0.1 }} viewport={{ once: true }}>
                        <div className="bg-secondary backdrop-blur-xl border rounded-xl p-4  transition-all duration-300">
                          <div className="flex items-center justify-between mb-3">
                            <div className="font-mono font-bold text-card-foreground">{part.part}</div>
                            <div className="flex items-center gap-2">
                              <Badge className="bg-chart-2/20 text-chart-2 border border-chart-2/30">{part.match} совместимость</Badge>
                              <Badge className="bg-primary text-primary-foreground border-0">{part.price}</Badge>
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground mb-3">{part.specs}</div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Наличие:</span>
                            <span className={`font-medium ${part.availability === "В наличии" ? "text-chart-2" : part.availability.includes("дня") ? "text-chart-4" : "text-muted-foreground"}`}>
                              {part.availability}
                            </span>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

