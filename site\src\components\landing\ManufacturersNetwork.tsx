"use client"

import type React from "react"
import { forwardRef, useRef } from "react"
import { cn } from "@/lib/utils"
import { AnimatedBeam } from "@/components/ui/animated-beam"

const Circle = forwardRef<HTMLDivElement, { className?: string; children?: React.ReactNode; label?: string }>(
  ({ className, children, label }, ref) => {
    return (
      <div className="flex flex-col items-center gap-2">
        <div
          ref={ref}
          className={cn(
            "z-10 flex size-12 items-center justify-center rounded-full border-2 bg-card p-3 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)]",
            className,
          )}
        >
          {children}
        </div>
        {label && (
          <div className="text-center">
            <div className="text-sm font-medium px-2 py-1 bg-card/80 rounded-lg border">
              {label}
            </div>
          </div>
        )}
      </div>
    )
  },
)

Circle.displayName = "Circle"

export function ManufacturersNetwork({ className }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null!)
  const div1Ref = useRef<HTMLDivElement>(null!)
  const div2Ref = useRef<HTMLDivElement>(null!)
  const div3Ref = useRef<HTMLDivElement>(null!)
  const div4Ref = useRef<HTMLDivElement>(null!)
  const div5Ref = useRef<HTMLDivElement>(null!)
  const div6Ref = useRef<HTMLDivElement>(null!)
  const div7Ref = useRef<HTMLDivElement>(null!)

  return (
    <div className="relative w-full h-[700px] bg-secondary rounded-2xl border overflow-hidden">
      <div
        className={cn("relative flex h-[500px] w-full items-center justify-center overflow-hidden p-10", className)}
        ref={containerRef}
      >
        <div className="flex size-full max-w-4xl flex-row items-stretch justify-between gap-10">
          {/* Производители слева */}
          <div className="flex flex-col justify-center gap-4">
            <Circle ref={div1Ref} label="Caterpillar">
              <Icons.caterpillar />
            </Circle>
            <Circle ref={div2Ref} label="John Deere">
              <Icons.johnDeere />
            </Circle>
            <Circle ref={div3Ref} label="Komatsu">
              <Icons.komatsu />
            </Circle>
            <Circle ref={div4Ref} label="Volvo">
              <Icons.volvo />
            </Circle>
            <Circle ref={div5Ref} label="Liebherr">
              <Icons.liebherr />
            </Circle>
          </div>

          {/* Каталог в центре */}
          <div className="flex flex-col justify-center">
            <Circle ref={div6Ref} className="size-20 bg-primary border-primary/50" label="Наш Каталог">
              <Icons.catalog />
            </Circle>
          </div>

          {/* Пользователь справа */}
          <div className="flex flex-col justify-center">
            <Circle ref={div7Ref} className="bg-chart-2/20 border-chart-2 text-foreground" label="Заказчик">
              <Icons.user />
            </Circle>
          </div>
        </div>

        {/* Анимированные лучи от производителей к каталогу */}
        <AnimatedBeam containerRef={containerRef} fromRef={div1Ref} toRef={div6Ref} />
        <AnimatedBeam containerRef={containerRef} fromRef={div2Ref} toRef={div6Ref} />
        <AnimatedBeam containerRef={containerRef} fromRef={div3Ref} toRef={div6Ref} />
        <AnimatedBeam containerRef={containerRef} fromRef={div4Ref} toRef={div6Ref} />
        <AnimatedBeam containerRef={containerRef} fromRef={div5Ref} toRef={div6Ref} />

        {/* Анимированный луч от каталога к пользователю */}
        <AnimatedBeam containerRef={containerRef} fromRef={div6Ref} toRef={div7Ref} />
      </div>

      {/* Info overlay */}
      <div className="absolute bottom-4 left-4 right-4">
        <div className="bg-card/90 backdrop-blur-xl border rounded-lg p-6">
          <h4 className="text-card-foreground font-semibold mb-3 text-2xl">Сеть производителей и поставщиков</h4>
          <p className="text-muted-foreground text-lg">
            Наш каталог объединяет ведущих мировых производителей в единую экосистему взаимозаменяемых запчастей с
            прямыми связями между поставщиками и заказчиками
          </p>
        </div>
      </div>
    </div>
  )
}

const Icons = {
  caterpillar: () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="#FCD34D" />
      <path d="M2 17L12 22L22 17" stroke="#FCD34D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M2 12L12 17L22 12" stroke="#FCD34D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  johnDeere: () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20Z"
        fill="#10B981"
      />
      <path d="M12 6V18M6 12H18" stroke="#10B981" strokeWidth="2" strokeLinecap="round" />
    </svg>
  ),
  komatsu: () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3 3H21V21H3V3Z" fill="#F59E0B" />
      <path d="M8 8H16V16H8V8Z" fill="white" />
    </svg>
  ),
  volvo: () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" fill="#3B82F6" />
      <path d="M8 12L12 8L16 12L12 16L8 12Z" fill="white" />
    </svg>
  ),
  liebherr: () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2L22 20H2L12 2Z" fill="#8B5CF6" />
      <circle cx="12" cy="14" r="3" fill="white" />
    </svg>
  ),
  catalog: () => (
    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 6H20M4 12H20M4 18H20" stroke="hsl(var(--primary-foreground))" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <circle cx="12" cy="12" r="10" stroke="hsl(var(--primary-foreground))" strokeWidth="2" fill="none" />
    </svg>
  ),
  user: () => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  ),
}

