"use client";

import React, { useEffect, useState } from "react";
import { Moon, Sun } from "lucide-react";
import { Button } from "@/components/ui/button";

export function ThemeToggle() {
  const [theme, setTheme] = useState<"light" | "dark">("light");
  const [mounted, setMounted] = useState(false);

  // Функция для получения темы с учетом предпочтений пользователя
  const getTheme = (): "light" | "dark" => {
    if (typeof window === "undefined") return "light";

    // Сначала проверяем localStorage
    const stored = localStorage.getItem("theme") as "light" | "dark";
    if (stored && ["light", "dark"].includes(stored)) {
      return stored;
    }

    // Затем проверяем системные предпочтения
    if (window.matchMedia("(prefers-color-scheme: dark)").matches) {
      return "dark";
    }

    return "light";
  };

  // Функция для применения темы
  const applyTheme = (newTheme: "light" | "dark") => {
    const root = document.documentElement;

    if (newTheme === "dark") {
      root.classList.add("dark");
    } else {
      root.classList.remove("dark");
    }

    localStorage.setItem("theme", newTheme);
    setTheme(newTheme);
  };

  // Переключение темы
  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    applyTheme(newTheme);
  };

  // Эффект для инициализации темы при монтировании
  useEffect(() => {
    const initialTheme = getTheme();
    applyTheme(initialTheme);
    setMounted(true);
  }, []);

  // Не рендерим до монтирования, чтобы избежать гидратационных ошибок
  if (!mounted) {
    return (
      <Button variant="ghost" size="icon" className="h-9 w-9">
        <Sun className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="h-9 w-9 relative overflow-hidden transition-colors"
      title={`Переключить на ${theme === "light" ? "темную" : "светлую"} тему`}
    >
      <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Переключить тему</span>
    </Button>
  );
}
