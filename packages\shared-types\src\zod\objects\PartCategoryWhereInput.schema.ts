/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { IntNullableFilterObjectSchema } from './IntNullableFilter.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';
import { PartCategoryNullableScalarRelationFilterObjectSchema } from './PartCategoryNullableScalarRelationFilter.schema';
import { PartCategoryListRelationFilterObjectSchema } from './PartCategoryListRelationFilter.schema';
import { PartListRelationFilterObjectSchema } from './PartListRelationFilter.schema';
import { CategoryPageConfigNullableScalarRelationFilterObjectSchema } from './CategoryPageConfigNullableScalarRelationFilter.schema';
import { CategoryPageConfigWhereInputObjectSchema } from './CategoryPageConfigWhereInput.schema';
import { MediaAssetNullableScalarRelationFilterObjectSchema } from './MediaAssetNullableScalarRelationFilter.schema';
import { MediaAssetWhereInputObjectSchema } from './MediaAssetWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryWhereInput>;
export const PartCategoryWhereInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => PartCategoryWhereInputObjectSchema),
    z.lazy(() => PartCategoryWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => PartCategoryWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => PartCategoryWhereInputObjectSchema),
    z.lazy(() => PartCategoryWhereInputObjectSchema).array()]).optional(), id: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), name: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), slug: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), description: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), parentId: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), level: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), path: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), icon: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), imageId: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), createdAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), parent: z.union([z.lazy(() => PartCategoryNullableScalarRelationFilterObjectSchema),
    z.lazy(() => PartCategoryWhereInputObjectSchema),
    z.null()]).optional().nullable(), children: z.lazy(() => PartCategoryListRelationFilterObjectSchema).optional().optional(), parts: z.lazy(() => PartListRelationFilterObjectSchema).optional().optional(), pageConfig: z.union([z.lazy(() => CategoryPageConfigNullableScalarRelationFilterObjectSchema),
    z.lazy(() => CategoryPageConfigWhereInputObjectSchema),
    z.null()]).optional().nullable(), image: z.union([z.lazy(() => MediaAssetNullableScalarRelationFilterObjectSchema),
    z.lazy(() => MediaAssetWhereInputObjectSchema),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
