/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { TemplateAreaSchema } from '../enums/TemplateArea.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EnumTemplateAreaFieldUpdateOperationsInput>;
export const EnumTemplateAreaFieldUpdateOperationsInputObjectSchema: SchemaType = z.object({
    set: z.lazy(() => TemplateAreaSchema).optional().optional()
}).strict() as SchemaType;
