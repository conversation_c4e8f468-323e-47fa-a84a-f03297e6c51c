<template>
  <div class="catalog-items-manager">
    <!-- Панель управления -->
    <VCard class="mb-6">
      <template #content>
        <div class="p-6">
          <div class="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <!-- Поиск и фильтры -->
            <div class="flex flex-col sm:flex-row gap-4 flex-1">
              <div class="flex-1">
                <VInputText
                  v-model="searchQuery"
                  placeholder="Поиск по артикулу, описанию или бренду..."
                  class="w-full"
                  @input="debouncedSearch"
                >
                  <template #prefix>
                    <SearchIcon />
                  </template>
                </VInputText>
              </div>
              
              <VAutoComplete
                v-model="selectedBrand"
                :suggestions="brandSuggestions"
                @complete="searchBrands"
                option-label="name"
                placeholder="Фильтр по бренду"
                class="w-full sm:w-64"
                @change="onBrandFilterChange"
                dropdown
              />
            </div>

            <!-- Кнопки действий -->
            <div class="flex gap-2">
              <VButton
                @click="showCreateDialog = true"
                severity="secondary"
                outlined
                label="Добавить позицию"
              >
                <PlusIcon />
              </VButton>
              <VButton
                @click="refreshData"
                severity="secondary"
                outlined
                :loading="loading"
              >
                <RefreshCcwIcon />
              </VButton>
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Таблица каталожных позиций -->
    <CatalogItemsTable
      :items="catalogItems"
      :loading="loading"
      :total-records="totalRecords"
      :rows="pageSize"
      :first="(currentPage - 1) * pageSize"
      @page="onPageChange"
      @sort="onSort"
      @edit="onEdit"
      @delete="onDelete"
      @view-details="onViewDetails"
      @match="onMatch"
    />

    <!-- Диалог создания/редактирования -->
    <VDialog
      v-model:visible="showCreateDialog"
      modal
      :header="editingItem ? 'Редактировать позицию' : 'Создать позицию'"
      class="w-auto flex"
    >
      <CatalogItemForm
        :item="editingItem"
        @save="onSave"
        @cancel="onCancel"
      />
    </VDialog>

    <!-- Диалог просмотра деталей -->
    <VDialog
      v-model:visible="showDetailsDialog"
      modal
      header="Детали позиции"
      class="w-auto"
    >
      <CatalogItemCard
        v-if="selectedItem"
        :item="selectedItem"
        @edit="onEditFromDetails"
        @match="onMatchFromCard"
        @close="showDetailsDialog = false"
        @unlink="onUnlink"
      />
    </VDialog>

    <!-- Диалог подбора взаимозаменяемости -->
    <VDialog v-model:visible="showMatchingDialog" modal header="Подбор взаимозаменяемых групп" class="w-auto">
      <div class="p-4 space-y-4" v-if="matchingItem">
        <!-- Конфигурация подбора: обязательные атрибуты -->
        <div class="flex flex-col md:flex-row md:items-end gap-3">
          <div class="flex-1">
            <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
              Обязательные атрибуты для совпадения
            </label>
            <VMultiSelect
              v-model="requiredTemplateIds"
              :options="attributeOptions"
              option-label="title"
              option-value="id"
              placeholder="Выберите атрибуты (рекомендуется 2+)"
              class="w-full"
            />
            <small class="text-surface-500">Чтобы исключить ложные совпадения по одному признаку — выберите ключевые атрибуты.</small>
          </div>
          <div class="flex gap-2">
            <VButton severity="secondary" outlined :loading="matchingLoading" @click="runMatching">Найти</VButton>
          </div>
        </div>

        <MatchingResults
          :item="matchingItem"
          :results="matchingResults"
          :loading="matchingLoading"
          @refresh="runMatching"
          @link="linkToPart"
        />
      </div>
    </VDialog>

    <!-- Диалог подтверждения удаления -->
    <VConfirmDialog />

    <!-- Toast контейнер для уведомлений в рамках этого изолята -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useTrpc } from '@/composables/useTrpc'
import { useUrlParams } from '@/composables/useUrlParams'
import { useConfirm } from '@/composables/useConfirm'
import { useToast } from '@/composables/useToast'
import VCard from '@/volt/Card.vue'
import VInputText from '@/volt/InputText.vue'
import VAutoComplete from '@/volt/AutoComplete.vue'
import VButton from '@/volt/Button.vue'
import VDialog from '@/volt/Dialog.vue'
import VConfirmDialog from '@/volt/ConfirmDialog.vue'
import VMultiSelect from '@/volt/MultiSelect.vue'
import Toast from '@/volt/Toast.vue'
import CatalogItemsTable from './CatalogItemsTable.vue'
import CatalogItemForm from './CatalogItemForm.vue'
import CatalogItemCard from './CatalogItemCard.vue'
import MatchingResults from './MatchingResults.vue'
import { SearchIcon } from 'lucide-vue-next'
import { PlusIcon } from 'lucide-vue-next'
import { RefreshCcwIcon } from 'lucide-vue-next'

// Composables
const { catalogItems: catalogItemsApi, brands, loading, error, matching, partApplicability, client } = useTrpc()
const catalogItemAttributesApi = client.crud.catalogItemAttribute
const confirm = useConfirm()
const toast = useToast()

// Состояние
const catalogItems = ref<any[]>([])
const totalRecords = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchQuery = ref('')
const selectedBrand = ref<any>(null)
const brandSuggestions = ref<any[]>([])

// URL-синхронизация фильтров/состояния таблицы (единый механизм)
const urlSync = useUrlParams({
  q: '',
  brandId: undefined as number | undefined,
  page: 1,
  rows: 20,
  sortField: '',
  sortOrder: 1 as 1 | -1,
}, {
  prefix: 'ci_',
  numberParams: ['brandId', 'page', 'rows'],
  debounceMs: 300,
})

// Диалоги
const showCreateDialog = ref(false)
const showDetailsDialog = ref(false)
const editingItem = ref<any>(null)
const selectedItem = ref<any>(null)
const matchingItem = ref<any>(null)
const showMatchingDialog = ref(false)
const matchingResults = ref<any[] | null>(null)
const matchingLoading = ref(false)

// Выбранные обязательные атрибуты для совпадения
const requiredTemplateIds = ref<number[]>([])

// Опции для выбора атрибутов текущей позиции
const attributeOptions = computed(() => {
  const item = matchingItem.value
  if (!item || !Array.isArray(item.attributes)) return [] as Array<{ id: number; title: string }>
  // Берём шаблоны атрибутов текущего CatalogItem
  const seen = new Set<number>()
  const options: Array<{ id: number; title: string }> = []
  for (const a of item.attributes) {
    const t = a.template
    if (!t) continue
    if (seen.has(t.id)) continue
    seen.add(t.id)
    options.push({ id: t.id, title: t.title || t.name || `#${t.id}` })
  }
  // Сортируем по названию
  return options.sort((a, b) => a.title.localeCompare(b.title))
})

// Сортировка
const sortField = ref<string>('')
const sortOrder = ref<number>(1)

// Методы
const loadCatalogItems = async () => {
  try {
    const filters: any = {
      skip: (currentPage.value - 1) * pageSize.value,
      take: pageSize.value,
      include: {
        brand: true,
        image: true,
        mediaAssets: true,
        attributes: {
          include: {
            template: true
          }
        },
        applicabilities: {
          include: {
            part: true
          }
        }
      }
    }

    // Поиск
    if (searchQuery.value.trim()) {
      filters.where = {
        OR: [
          { sku: { contains: searchQuery.value.trim(), mode: 'insensitive' } },
          { description: { contains: searchQuery.value.trim(), mode: 'insensitive' } },
          { brand: { name: { contains: searchQuery.value.trim(), mode: 'insensitive' } } }
        ]
      }
    }

    // Фильтр по бренду
    if (selectedBrand.value) {
      const brandFilter = { brandId: selectedBrand.value.id }
      if (filters.where) {
        filters.where = { AND: [filters.where, brandFilter] }
      } else {
        filters.where = brandFilter
      }
    }

    // Сортировка
    if (sortField.value) {
      filters.orderBy = { [sortField.value]: sortOrder.value === 1 ? 'asc' : 'desc' }
    } else {
      filters.orderBy = { id: 'desc' }
    }

    const result = (await catalogItemsApi.findMany(filters)) as any[] | null
    if (Array.isArray(result)) {
      catalogItems.value = result
    } else {
      catalogItems.value = []
    }

    // Получаем общее количество записей
    const countResult = (await catalogItemsApi.findMany({
      where: filters.where,
      select: { id: true }
    })) as any[] | null
    totalRecords.value = Array.isArray(countResult) ? countResult.length : 0
  } catch (err) {
    console.error('Ошибка загрузки каталожных позиций:', err)
    toast.error('Ошибка', 'Не удалось загрузить каталожные позиции')
  }
}

const searchBrands = async (event: any) => {
  try {
    const query = event.query.toLowerCase()
    const result = (await brands.findMany({
      where: {
        name: { contains: query, mode: 'insensitive' }
      },
      take: 10
    })) as any[] | null
    brandSuggestions.value = Array.isArray(result) ? result : []
  } catch (err) {
    console.error('Ошибка поиска брендов:', err)
  }
}

// Debounced search
let searchTimeout: NodeJS.Timeout
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    urlSync.updateFilters({ q: searchQuery.value || undefined, page: 1 })
    loadCatalogItems()
  }, 300)
}

const onBrandFilterChange = () => {
  currentPage.value = 1
  urlSync.updateFilters({ brandId: selectedBrand.value?.id, page: 1 })
  loadCatalogItems()
}

const onPageChange = (event: any) => {
  currentPage.value = Math.floor(event.first / event.rows) + 1
  pageSize.value = event.rows
  urlSync.updateFilters({ page: currentPage.value, rows: pageSize.value })
  loadCatalogItems()
}

const onSort = (event: any) => {
  sortField.value = event.sortField
  sortOrder.value = event.sortOrder
  urlSync.updateFilters({ sortField: sortField.value, sortOrder: (sortOrder.value as 1 | -1) })
  loadCatalogItems()
}

const refreshData = () => {
  loadCatalogItems()
}

const onEdit = (item: any) => {
  editingItem.value = { ...item }
  showCreateDialog.value = true
}

const onDelete = (item: any) => {
  confirm.confirmDelete(
    `позицию "${item.sku}" (${item.brand?.name})`,
    async () => {
      try {
        await catalogItemsApi.delete({ where: { id: item.id } })
        toast.success('Успешно', 'Позиция удалена')
        loadCatalogItems()
      } catch (err) {
        console.error('Ошибка удаления:', err)
        toast.error('Ошибка', 'Не удалось удалить позицию')
      }
    }
  )
}

const onViewDetails = (item: any) => {
  selectedItem.value = item
  showDetailsDialog.value = true
}

const onMatch = (item: any) => {
  matchingItem.value = item
  showMatchingDialog.value = true
  runMatching()
}

const runMatching = async () => {
  if (!matchingItem.value) return
  matchingLoading.value = true
  matchingResults.value = null
  try {
    const res = await matching.findMatchingParts({ catalogItemId: matchingItem.value.id, requiredTemplateIds: requiredTemplateIds.value.length ? requiredTemplateIds.value : undefined })
    matchingResults.value = res ? (res as any).candidates || [] : []
    // Больше не делаем авто-привязку без подтверждения — всегда через диалог MatchingResults
  } catch (err) {
    console.error('Ошибка подбора:', err)
    matchingResults.value = []
  } finally {
    matchingLoading.value = false
  }
}

const linkToPart = async (payload: { partId: number; accuracy: 'EXACT_MATCH' | 'MATCH_WITH_NOTES' | 'REQUIRES_MODIFICATION' | 'PARTIAL_MATCH'; notes?: string }) => {
  if (!matchingItem.value) return
  try {
    await partApplicability.upsert({
      where: { partId_catalogItemId: { partId: payload.partId, catalogItemId: matchingItem.value.id } },
      create: { partId: payload.partId, catalogItemId: matchingItem.value.id, accuracy: payload.accuracy, notes: payload.notes },
      update: { accuracy: payload.accuracy, notes: payload.notes }
    })
    // toast.success перенесён в useTrpc через success-опцию execute; оставить локально нет необходимости
    showMatchingDialog.value = false
    loadCatalogItems()
  } catch (err) {
    console.error('Ошибка привязки:', err)
    toast.error('Ошибка', 'Не удалось привязать позицию')
  }
}

const onSave = async (itemData: any) => {
  try {
    // Извлекаем атрибуты из данных (они не поддерживаются в nested operations)
    const { attributes, ...catalogItemData } = itemData
    
    let catalogItem: any = null
    
    if (editingItem.value) {
      catalogItem = await catalogItemsApi.update({
        where: { id: editingItem.value.id },
        data: catalogItemData
      })
      
      // Удаляем старые атрибуты
      if (attributes?.deleteMany !== undefined) {
        await catalogItemAttributesApi.deleteMany.mutate({
          where: { catalogItemId: editingItem.value.id }
        })
      }
    } else {
      catalogItem = await catalogItemsApi.create({ data: catalogItemData })
    }
    
    // Создаем атрибуты отдельно, если они есть
    if (attributes?.create && attributes.create.length > 0) {
      for (const attr of attributes.create) {
        try {
          await catalogItemAttributesApi.create.mutate({
            data: {
              value: String(attr.value), // Преобразуем в строку
              catalogItem: {
                connect: { id: catalogItem.id }
              },
              template: {
                connect: { id: attr.templateId }
              }
            }
          })
        } catch (attrErr) {
          console.error('Ошибка создания атрибута:', attrErr)
        }
      }
    }
    
    // Успешный тост показывается из useTrpc через success-опцию execute
    
    showCreateDialog.value = false
    editingItem.value = null
    loadCatalogItems()
  } catch (err) {
    console.error('❌ Ошибка сохранения:', err)
    toast.error('Ошибка', 'Не удалось сохранить позицию')
  }
}

const onCancel = () => {
  showCreateDialog.value = false
  editingItem.value = null
}

const onEditFromDetails = () => {
  editingItem.value = { ...selectedItem.value }
  showDetailsDialog.value = false
  showCreateDialog.value = true
}

const onMatchFromCard = () => {
  matchingItem.value = selectedItem.value
  showMatchingDialog.value = true
  runMatching()
}

const onUnlink = async (applicability: any) => {
  if (!applicability?.id) return
  confirm.show({
    header: 'Удалить связь?',
    message: `Отвязать позицию от группы #${applicability.part?.id || ''}?`,
    icon: 'pi pi-trash',
    acceptLabel: 'Отвязать',
    rejectLabel: 'Отмена',
    acceptClass: 'bg-red-500 hover:bg-red-600',
    accept: async () => {
      try {
        await partApplicability.delete({ where: { id: applicability.id } })
        // Локально обновляем выбранный item, чтобы не мигать диалогом
        if (selectedItem.value) {
          selectedItem.value = {
            ...selectedItem.value,
            applicabilities: (selectedItem.value.applicabilities || []).filter((a: any) => a.id !== applicability.id)
          }
        }
        // Обновим таблицу
        loadCatalogItems()
      } catch (err) {
        console.error('Ошибка отвязки:', err)
        toast.error('Ошибка', 'Не удалось отвязать позицию')
      }
    }
  })
}

// Инициализация
// Применяем URL в локальный стейт при монтировании
onMounted(() => {
  const f = urlSync.filters.value as any
  searchQuery.value = f.q || ''
  selectedBrand.value = f.brandId ? { id: f.brandId } : null
  currentPage.value = f.page || 1
  pageSize.value = f.rows || 20
  sortField.value = f.sortField || ''
  sortOrder.value = (f.sortOrder === -1 ? -1 : 1)
  loadCatalogItems()
})

// Поддерживаем двустороннюю синхронизацию при навигации назад/вперёд
watch(urlSync.filters, (f) => {
  const next = f as any
  const nextBrand = next.brandId ? { id: next.brandId } : null
  if (searchQuery.value !== (next.q || '')) searchQuery.value = next.q || ''
  if ((selectedBrand.value?.id || null) !== (next.brandId ?? null)) selectedBrand.value = nextBrand
  if (currentPage.value !== (next.page || 1)) currentPage.value = next.page || 1
  if (pageSize.value !== (next.rows || 20)) pageSize.value = next.rows || 20
  if (sortField.value !== (next.sortField || '')) sortField.value = next.sortField || ''
  if (sortOrder.value !== (next.sortOrder === -1 ? -1 : 1)) sortOrder.value = (next.sortOrder === -1 ? -1 : 1)
  loadCatalogItems()
})
</script>
