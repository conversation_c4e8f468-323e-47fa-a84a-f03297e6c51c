/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntWithAggregatesFilterObjectSchema } from './IntWithAggregatesFilter.schema';
import { EnumTemplateAreaWithAggregatesFilterObjectSchema } from './EnumTemplateAreaWithAggregatesFilter.schema';
import { TemplateAreaSchema } from '../enums/TemplateArea.schema';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { StringNullableWithAggregatesFilterObjectSchema } from './StringNullableWithAggregatesFilter.schema';
import { JsonWithAggregatesFilterObjectSchema } from './JsonWithAggregatesFilter.schema';
import { BoolWithAggregatesFilterObjectSchema } from './BoolWithAggregatesFilter.schema';
import { DateTimeWithAggregatesFilterObjectSchema } from './DateTimeWithAggregatesFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PageTemplateScalarWhereWithAggregatesInput>;
export const PageTemplateScalarWhereWithAggregatesInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => PageTemplateScalarWhereWithAggregatesInputObjectSchema),
    z.lazy(() => PageTemplateScalarWhereWithAggregatesInputObjectSchema).array()]).optional(), OR: z.lazy(() => PageTemplateScalarWhereWithAggregatesInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => PageTemplateScalarWhereWithAggregatesInputObjectSchema),
    z.lazy(() => PageTemplateScalarWhereWithAggregatesInputObjectSchema).array()]).optional(), id: z.union([z.lazy(() => IntWithAggregatesFilterObjectSchema),
    z.number()]).optional(), area: z.union([z.lazy(() => EnumTemplateAreaWithAggregatesFilterObjectSchema),
    z.lazy(() => TemplateAreaSchema)]).optional(), slug: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema),
    z.string()]).optional(), title: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema),
    z.string()]).optional(), description: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), defaultConfig: z.lazy(() => JsonWithAggregatesFilterObjectSchema).optional().optional(), enabled: z.union([z.lazy(() => BoolWithAggregatesFilterObjectSchema),
    z.boolean()]).optional(), createdAt: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional()
}).strict() as SchemaType;
