/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CategoryPageConfigWhereInputObjectSchema } from './CategoryPageConfigWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigNullableScalarRelationFilter>;
export const CategoryPageConfigNullableScalarRelationFilterObjectSchema: SchemaType = z.object({
    is: z.union([z.lazy(() => CategoryPageConfigWhereInputObjectSchema),
    z.null()]).optional().nullable(), isNot: z.union([z.lazy(() => CategoryPageConfigWhereInputObjectSchema),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
