export type AttributeTemplate = {
  id: number
  name: string
  title: string
  dataType: "STRING" | "NUMBER" | "BOOLEAN" | "DATE" | "JSON"
  unit: string | null
  isRequired: boolean
  allowedValues: string[]
  tolerance: number | null
}

export type CatalogSearchFilters = {
  query: string
  categoryIds: number[]
  brandIds: number[]
  isOemOnly: boolean
  attributeFilters: Record<
    number,
    {
      values?: string[]
      numericRange?: [number | undefined, number | undefined]
    }
  >
}
