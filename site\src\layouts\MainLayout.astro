---
import "../styles/global.css";
import { AuthNav } from "@/components/navigation/AuthNav";
import { ThemeToggle } from "@/components/theme/ThemeToggle";
import { ClientRouter } from "astro:transitions";

interface Props {
  title: string;
  description?: string;
}

const { title, description = "PartTec3 - Каталог взаимозаменяемых запчастей" } =
  Astro.props;
---

<html lang="ru">
  <head>
    <ClientRouter />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content={description} />
    <title>{title} | PartTec3</title>
    <meta name="view-transition" content="same-origin" />
  </head>
  <body class="min-h-screen bg-background font-sans antialiased">
    <!-- Скрипт для сохранения темы при view transitions -->
    <script is:inline>
      // Функция для применения темы
      function applyTheme(theme) {
        const root = document.documentElement;
        if (theme === 'dark') {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      }

      // Функция для получения текущей темы
      function getStoredTheme() {
        if (typeof window === 'undefined') return 'light';
        const stored = localStorage.getItem('theme');
        if (stored && ['light', 'dark'].includes(stored)) {
          return stored;
        }
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          return 'dark';
        }
        return 'light';
      }

      // Применяем тему при загрузке страницы
      applyTheme(getStoredTheme());

      // Сохраняем тему при переходах между страницами
      document.addEventListener('astro:before-swap', (event) => {
        const theme = getStoredTheme();
        applyTheme(theme);

        // Применяем тему к новому документу перед свапом
        if (event.newDocument) {
          applyTheme(theme);
        }
      });

      // Применяем тему после свапа страницы
      document.addEventListener('astro:after-swap', () => {
        applyTheme(getStoredTheme());
      });
    </script>

    <header
      class="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
    >
      <div class="container flex h-14 max-w-screen-2xl items-center">
        <!-- Логотип для мобильной версии -->
        <div class="flex md:hidden items-center gap-2">
          <a href="/" class="flex items-center space-x-2">
            <span class="font-bold text-lg">PartTec3</span>
          </a>
        </div>

        <!-- Логотип для десктопа -->
        <div class="hidden md:flex items-center gap-2">
          <a href="/" class="flex items-center space-x-2">
            <span class="font-bold text-xl">PartTec3</span>
          </a>
        </div>

        <!-- Правая часть хедера -->
        <div class="ml-auto flex items-center gap-2">
          <!-- Переключатель тем -->
          <ThemeToggle client:load />
          <!-- Навигация и меню -->
          <AuthNav client:load />
        </div>
      </div>
    </header>

    <main class="flex-1">
      <slot />
    </main>

    <footer class="border-t py-6 md:py-0">
      <div
        class="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row"
      >
        <div
          class="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0"
        >
          <p
            class="text-center text-sm leading-loose text-muted-foreground md:text-left"
          >
            © 2024 PartTec3. Каталог взаимозаменяемых запчастей.
          </p>
        </div>
      </div>
    </footer>
  </body>
</html>
