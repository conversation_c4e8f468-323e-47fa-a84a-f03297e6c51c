/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CategoryPageConfigUpdateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUpdateWithoutCategoryInput.schema';
import { CategoryPageConfigUncheckedUpdateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUncheckedUpdateWithoutCategoryInput.schema';
import { CategoryPageConfigCreateWithoutCategoryInputObjectSchema } from './CategoryPageConfigCreateWithoutCategoryInput.schema';
import { CategoryPageConfigUncheckedCreateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUncheckedCreateWithoutCategoryInput.schema';
import { CategoryPageConfigWhereInputObjectSchema } from './CategoryPageConfigWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigUpsertWithoutCategoryInput>;
export const CategoryPageConfigUpsertWithoutCategoryInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => CategoryPageConfigUpdateWithoutCategoryInputObjectSchema), z.lazy(() => CategoryPageConfigUncheckedUpdateWithoutCategoryInputObjectSchema)]), create: z.union([z.lazy(() => CategoryPageConfigCreateWithoutCategoryInputObjectSchema), z.lazy(() => CategoryPageConfigUncheckedCreateWithoutCategoryInputObjectSchema)]), where: z.lazy(() => CategoryPageConfigWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
