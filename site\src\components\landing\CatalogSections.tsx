"use client"

import { motion } from "motion/react"
import { Cpu, Database, Factory, Settings, ChevronRight, Clock, type LucideIcon } from "lucide-react"
import { Button } from "@/components/ui/button"

type Section = {
  id: string
  name: string
  icon: LucideIcon
  active: boolean
  description: string
  detailedInfo: {
    totalParts: string
    manufacturers: string
    categories: string[]
    specifications: string[]
    applications: string[]
  }
}

const catalogSectionsData: readonly Section[] = [
  {
    id: "seals",
    name: "Сальники и уплотнения",
    icon: Settings,
    active: true,
    description: "Комплексная база данных сальников, манжет и уплотнительных элементов",
    detailedInfo: {
      totalParts: "52,847",
      manufacturers: "247",
      categories: [
        "Радиальные сальники (ГОСТ 8752-79, DIN 3760)",
        "Манжеты гидроцилиндров (ГОСТ 14896-84)",
        "V-образные манжеты (ГОСТ 22704-77)",
        "Уплотнения поршневые и штоковые",
        "O-кольца (ГОСТ 9833-73, ISO 3601)",
        "Грязесъемники и направляющие кольца",
      ],
      specifications: [
        "Диаметры: от 6мм до 2000мм",
        "Материалы: NBR, FKM, PTFE, PU, EPDM",
        "Температурный диапазон: -60°C до +300°C",
        "Давление: до 700 бар",
      ],
      applications: [
        "Гидравлические системы",
        "Пневматические системы",
        "Автомобильная промышленность",
        "Сельскохозяйственная техника",
        "Строительная техника",
        "Промышленное оборудование",
      ],
    },
  },
  {
    id: "filters",
    name: "Фильтрующие элементы",
    icon: Database,
    active: false,
    description: "Масляные, воздушные, топливные и гидравлические фильтры",
    detailedInfo: {
      totalParts: "Скоро",
      manufacturers: "150+",
      categories: [
        "Масляные фильтры двигателей",
        "Воздушные фильтры и элементы",
        "Топливные фильтры и сепараторы",
        "Гидравлические фильтры",
        "Салонные фильтры",
        "Сепараторы масла и воздуха",
      ],
      specifications: [
        "Степень фильтрации: от 1 до 200 микрон",
        "Рабочее давление: до 350 бар",
        "Температурный режим: -40°C до +150°C",
        "Типы соединений: резьбовые, фланцевые, байонетные",
      ],
      applications: [
        "Двигатели внутреннего сгорания",
        "Гидравлические системы",
        "Компрессорное оборудование",
        "Системы вентиляции",
      ],
    },
  },
]

export function CatalogSections() {
  return (
    <div className="relative container mx-auto px-4">
      <div className="text-center mb-12">
        <motion.div initial={{ opacity: 0, scale: 0.8 }} whileInView={{ opacity: 1, scale: 1 }} transition={{ duration: 0.8 }} viewport={{ once: true }} className="inline-flex items-center gap-2 bg-secondary border text-primary px-4 py-2 rounded-full text-base font-medium mb-3">
          <Database className="w-4 h-4" />
          Промышленный каталог
        </motion.div>
        <h2 className="text-5xl lg:text-5xl font-bold mb-3">
          <span className="text-muted-foreground">Разделы промышленного</span>{" "}
          <span className="text-primary">каталога</span>
        </h2>
        <p className="text-2xl text-muted-foreground">Комплексная система каталогизации запчастей по техническим категориям</p>
      </div>

      <div className="space-y-12">
        {catalogSectionsData.map((section, index) => {
          const Icon = section.icon
          return (
            <motion.div key={section.id} initial={{ opacity: 0, x: index % 2 === 0 ? -60 : 60 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.8, delay: index * 0.1 }} viewport={{ once: true }}>
              <div className={`bg-card backdrop-blur-xl border rounded-2xl ${section.active ? "" : "opacity-75"}`}>
                <div className="grid lg:grid-cols-3 gap-0">
                  {/* Header Section */}
                  <div className="p-6 bg-background">
                    <div className="flex items-center gap-4 mb-3">
                      <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${section.active ? "bg-primary" : "bg-muted"}`}>
                        <Icon className="w-8 h-8 text-primary-foreground" />
                      </div>
                      <div>
                        <h3 className="text-3xl font-bold text-card-foreground">{section.name}</h3>
                        {section.active && (
                          <div className="flex items-center gap-2 mt-2">
                            <div className="w-2 h-2 bg-chart-2 rounded-full" />
                            <span className="text-chart-2 text-sm font-medium">Активен</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <p className="text-lg text-muted-foreground mb-4">{section.description}</p>
                    <div className="grid grid-cols-2 gap-6">
                      <div className="bg-secondary backdrop-blur-xl border rounded-xl p-4">
                        <div className={`text-3xl font-bold mb-1 ${section.active ? "text-primary" : "text-muted-foreground"}`}>{section.detailedInfo.totalParts}</div>
                        <div className="text-sm text-muted-foreground">Позиций в каталоге</div>
                      </div>
                      <div className="bg-secondary backdrop-blur-xl border rounded-xl p-4">
                        <div className={`text-3xl font-bold mb-1 ${section.active ? "text-chart-2" : "text-muted-foreground"}`}>{section.detailedInfo.manufacturers}</div>
                        <div className="text-sm text-muted-foreground">Производителей</div>
                      </div>
                    </div>
                    {!section.active && (
                      <div className="mt-4 p-4 bg-secondary border rounded-xl backdrop-blur-xl">
                        <div className="flex items-center gap-2 text-chart-4 text-sm font-medium">
                          <Clock className="w-4 h-4" />
                          Раздел появится в ближайшее время
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Categories Section */}
                  <div className="p-6 bg-background border-l">
                    <h4 className="font-semibold text-card-foreground mb-4 flex items-center gap-3">
                      <div className="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                        <Cpu className="w-4 h-4 text-secondary-foreground" />
                      </div>
                      Категории деталей
                    </h4>
                    <div className="space-y-3 mb-4">
                      {section.detailedInfo.categories.map((category, i) => (
                        <motion.div key={i} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ delay: i * 0.1 }} viewport={{ once: true }} className="flex items-center gap-3 text-sm group">
                          <ChevronRight className="w-4 h-4 text-muted group-hover:text-primary transition-colors" />
                          <span className={`${section.active ? "text-muted-foreground" : "text-muted"} group-hover:text-foreground transition-colors`}>{category}</span>
                        </motion.div>
                      ))}
                    </div>
                    <div className="bg-secondary backdrop-blur-xl border rounded-xl p-4">
                      <h5 className="font-medium text-card-foreground mb-3 flex items-center gap-2">
                        <Settings className="w-4 h-4 text-primary" />
                        Технические характеристики:
                      </h5>
                      <div className="space-y-2">
                        {section.detailedInfo.specifications.map((spec, i) => (
                          <div key={i} className="text-sm text-muted-foreground flex items-center gap-2">
                            <div className="w-1 h-1 bg-primary rounded-full" />
                            {spec}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Applications Section */}
                  <div className="p-6 bg-background border-l">
                    <h4 className="font-semibold text-card-foreground mb-4 flex items-center gap-3">
                      <div className="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                        <Factory className="w-4 h-4 text-secondary-foreground" />
                      </div>
                      Области применения
                    </h4>
                    <div className="space-y-4 mb-4">
                      {section.detailedInfo.applications.map((app, i) => (
                        <motion.div key={i} initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} transition={{ delay: i * 0.1 }} viewport={{ once: true }}>
                          <div className="flex items-center gap-4 p-4 bg-secondary backdrop-blur-xl rounded-xl border  transition-all duration-300">
                            <Factory className="w-5 h-5 text-muted-foreground" />
                            <span className={`text-sm ${section.active ? "text-muted-foreground" : "text-muted"}`}>{app}</span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                    {section.active && (
                      <Button className="w-full">
                        Перейти к каталогу
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>
    </div>
  )
}

