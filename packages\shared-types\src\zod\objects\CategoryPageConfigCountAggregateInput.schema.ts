/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigCountAggregateInputType>;
export const CategoryPageConfigCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), categoryId: z.literal(true).optional().optional(), listTemplateSlug: z.literal(true).optional().optional(), detailTemplateSlug: z.literal(true).optional().optional(), listConfig: z.literal(true).optional().optional(), detailConfig: z.literal(true).optional().optional(), variables: z.literal(true).optional().optional(), textTemplates: z.literal(true).optional().optional(), seo: z.literal(true).optional().optional(), enabled: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
