import 'catalog_schema'

// Системные модели (не доменные), связанные с конфигурацией страниц каталога

// Тип сущности, которую листим на странице категории
enum ListEntity {
    PART
    ITEM
}

/// Конфигурация страниц для категории каталога
model CategoryPageConfig {
    id                Int           @id @default(autoincrement())

    // Привязка к категории (1:1)
    category          PartCategory  @relation("CategoryPageConfigOnPartCategory", fields: [categoryId], references: [id], onDelete: Cascade)
    categoryId        Int           @unique

    // Что показываем в листинге (товары или эталонные группы)
    listEntity        ListEntity    @default(ITEM)

    // Выбранные шаблоны (slug совпадает с реестром компонент на фронтенде)
    listTemplateSlug   String
    detailTemplateSlug String

    // Конфиги шаблонов (валидируются Zod на уровне API/UI)
    listConfig         Json
    detailConfig       Json

    // Определение переменных и текстовых шаблонов (H1/Title/Meta)
    variables          Json
    textTemplates      Json

    // Дополнительные SEO/настройки (опционально)
    seo                Json?

    enabled            Boolean       @default(true)
    createdAt          DateTime      @default(now())
    updatedAt          DateTime      @updatedAt

    @@index([categoryId])

    // Права доступа: чтение публично, запись только ADMIN
    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}
