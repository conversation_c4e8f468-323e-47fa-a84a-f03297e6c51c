/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntWithAggregatesFilterObjectSchema } from './IntWithAggregatesFilter.schema';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { JsonWithAggregatesFilterObjectSchema } from './JsonWithAggregatesFilter.schema';
import { JsonNullableWithAggregatesFilterObjectSchema } from './JsonNullableWithAggregatesFilter.schema';
import { BoolWithAggregatesFilterObjectSchema } from './BoolWithAggregatesFilter.schema';
import { DateTimeWithAggregatesFilterObjectSchema } from './DateTimeWithAggregatesFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigScalarWhereWithAggregatesInput>;
export const CategoryPageConfigScalarWhereWithAggregatesInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => CategoryPageConfigScalarWhereWithAggregatesInputObjectSchema),
    z.lazy(() => CategoryPageConfigScalarWhereWithAggregatesInputObjectSchema).array()]).optional(), OR: z.lazy(() => CategoryPageConfigScalarWhereWithAggregatesInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => CategoryPageConfigScalarWhereWithAggregatesInputObjectSchema),
    z.lazy(() => CategoryPageConfigScalarWhereWithAggregatesInputObjectSchema).array()]).optional(), id: z.union([z.lazy(() => IntWithAggregatesFilterObjectSchema),
    z.number()]).optional(), categoryId: z.union([z.lazy(() => IntWithAggregatesFilterObjectSchema),
    z.number()]).optional(), listTemplateSlug: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema),
    z.string()]).optional(), detailTemplateSlug: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema),
    z.string()]).optional(), listConfig: z.lazy(() => JsonWithAggregatesFilterObjectSchema).optional().optional(), detailConfig: z.lazy(() => JsonWithAggregatesFilterObjectSchema).optional().optional(), variables: z.lazy(() => JsonWithAggregatesFilterObjectSchema).optional().optional(), textTemplates: z.lazy(() => JsonWithAggregatesFilterObjectSchema).optional().optional(), seo: z.lazy(() => JsonNullableWithAggregatesFilterObjectSchema).optional().optional(), enabled: z.union([z.lazy(() => BoolWithAggregatesFilterObjectSchema),
    z.boolean()]).optional(), createdAt: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional()
}).strict() as SchemaType;
