/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { PageTemplateCountOrderByAggregateInputObjectSchema } from './PageTemplateCountOrderByAggregateInput.schema';
import { PageTemplateAvgOrderByAggregateInputObjectSchema } from './PageTemplateAvgOrderByAggregateInput.schema';
import { PageTemplateMaxOrderByAggregateInputObjectSchema } from './PageTemplateMaxOrderByAggregateInput.schema';
import { PageTemplateMinOrderByAggregateInputObjectSchema } from './PageTemplateMinOrderByAggregateInput.schema';
import { PageTemplateSumOrderByAggregateInputObjectSchema } from './PageTemplateSumOrderByAggregateInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PageTemplateOrderByWithAggregationInput>;
export const PageTemplateOrderByWithAggregationInputObjectSchema: SchemaType = z.object({
    id: z.lazy(() => SortOrderSchema).optional().optional(), area: z.lazy(() => SortOrderSchema).optional().optional(), slug: z.lazy(() => SortOrderSchema).optional().optional(), title: z.lazy(() => SortOrderSchema).optional().optional(), description: z.union([z.lazy(() => SortOrderSchema),
    z.lazy(() => SortOrderInputObjectSchema)]).optional(), defaultConfig: z.lazy(() => SortOrderSchema).optional().optional(), enabled: z.lazy(() => SortOrderSchema).optional().optional(), createdAt: z.lazy(() => SortOrderSchema).optional().optional(), updatedAt: z.lazy(() => SortOrderSchema).optional().optional(), _count: z.lazy(() => PageTemplateCountOrderByAggregateInputObjectSchema).optional().optional(), _avg: z.lazy(() => PageTemplateAvgOrderByAggregateInputObjectSchema).optional().optional(), _max: z.lazy(() => PageTemplateMaxOrderByAggregateInputObjectSchema).optional().optional(), _min: z.lazy(() => PageTemplateMinOrderByAggregateInputObjectSchema).optional().optional(), _sum: z.lazy(() => PageTemplateSumOrderByAggregateInputObjectSchema).optional().optional()
}).strict() as SchemaType;
