/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CategoryPageConfigWhereUniqueInputObjectSchema } from './CategoryPageConfigWhereUniqueInput.schema';
import { CategoryPageConfigCreateWithoutCategoryInputObjectSchema } from './CategoryPageConfigCreateWithoutCategoryInput.schema';
import { CategoryPageConfigUncheckedCreateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUncheckedCreateWithoutCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigCreateOrConnectWithoutCategoryInput>;
export const CategoryPageConfigCreateOrConnectWithoutCategoryInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CategoryPageConfigWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => CategoryPageConfigCreateWithoutCategoryInputObjectSchema), z.lazy(() => CategoryPageConfigUncheckedCreateWithoutCategoryInputObjectSchema)])
}).strict() as SchemaType;
