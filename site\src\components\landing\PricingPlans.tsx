"use client"

import { motion } from "motion/react"
import { Check } from "lucide-react"
import { Button } from "@/components/ui/button"

export function PricingPlans() {
  const plans = [
    {
      name: "Бесплатный",
      price: "0 ₽",
      period: "/ месяц",
      description: "Для ознакомления с базовыми возможностями каталога",
      features: [
        "Базовый поиск по каталогу",
        "Ограниченный доступ к техническим характеристикам",
        "До 20 запросов в месяц",
      ],
      cta: "Попробовать",
      highlighted: false,
    },
    {
      name: "Профессиональный",
      price: "2 990 ₽",
      period: "/ месяц",
      description: "Для инженеров и специалистов по закупкам",
      features: [
        "Полный доступ к базе взаимозаменяемости",
        "Расширенные фильтры и технические параметры",
        "ИИ-помощник поиска",
        "Экспорт данных",
        "Приоритетная поддержка",
      ],
      cta: "Оформить",
      highlighted: true,
    },
    {
      name: "Предприятие",
      price: "Запрос",
      period: "",
      description: "Индивидуальные условия для организаций",
      features: [
        "Доступ по API",
        "Интеграции с ERP/CMMS",
        "Обучение персонала",
        "Индивидуальные SLA",
      ],
      cta: "Связаться",
      highlighted: false,
    },
  ] as const

  return (
    <div className="relative container mx-auto px-4">
      <div className="text-center mb-12">
        <h2 className="text-5xl lg:text-5xl font-bold mb-3">
          <span className="text-muted-foreground">Тарифные планы</span>
        </h2>
        <p className="text-2xl text-muted-foreground">Выберите подходящий план и начните работу</p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.name}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
            className={`rounded-2xl border ${
              plan.highlighted ? "border-primary bg-card" : "border-border bg-card/80"
            } backdrop-blur-xl p-6`}
          >
            <div className="mb-4">
              <div className={`text-xl font-semibold ${plan.highlighted ? "text-primary" : "text-foreground"}`}>{plan.name}</div>
            </div>
            <div className="flex items-end gap-2 mb-3">
              <div className="text-4xl font-bold text-foreground">{plan.price}</div>
              <div className="text-muted-foreground">{plan.period}</div>
            </div>
            <div className="text-muted-foreground mb-4">{plan.description}</div>
            <div className="space-y-2 mb-6">
              {plan.features.map((f) => (
                <div key={f} className="flex items-center gap-2 text-sm">
                  <Check className="w-4 h-4 text-chart-2" />
                  <span className="text-muted-foreground">{f}</span>
                </div>
              ))}
            </div>
            <Button variant={plan.highlighted ? 'default' : 'secondary'} className="w-full">
              {plan.cta}
            </Button>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

