/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CategoryPageConfigCreateWithoutCategoryInputObjectSchema } from './CategoryPageConfigCreateWithoutCategoryInput.schema';
import { CategoryPageConfigUncheckedCreateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUncheckedCreateWithoutCategoryInput.schema';
import { CategoryPageConfigCreateOrConnectWithoutCategoryInputObjectSchema } from './CategoryPageConfigCreateOrConnectWithoutCategoryInput.schema';
import { CategoryPageConfigUpsertWithoutCategoryInputObjectSchema } from './CategoryPageConfigUpsertWithoutCategoryInput.schema';
import { CategoryPageConfigWhereInputObjectSchema } from './CategoryPageConfigWhereInput.schema';
import { CategoryPageConfigWhereUniqueInputObjectSchema } from './CategoryPageConfigWhereUniqueInput.schema';
import { CategoryPageConfigUpdateToOneWithWhereWithoutCategoryInputObjectSchema } from './CategoryPageConfigUpdateToOneWithWhereWithoutCategoryInput.schema';
import { CategoryPageConfigUpdateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUpdateWithoutCategoryInput.schema';
import { CategoryPageConfigUncheckedUpdateWithoutCategoryInputObjectSchema } from './CategoryPageConfigUncheckedUpdateWithoutCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigUpdateOneWithoutCategoryNestedInput>;
export const CategoryPageConfigUpdateOneWithoutCategoryNestedInputObjectSchema: SchemaType = z.object({
    create: z.union([z.lazy(() => CategoryPageConfigCreateWithoutCategoryInputObjectSchema), z.lazy(() => CategoryPageConfigUncheckedCreateWithoutCategoryInputObjectSchema)]).optional(), connectOrCreate: z.lazy(() => CategoryPageConfigCreateOrConnectWithoutCategoryInputObjectSchema).optional().optional(), upsert: z.lazy(() => CategoryPageConfigUpsertWithoutCategoryInputObjectSchema).optional().optional(), disconnect: z.union([z.boolean(),
    z.lazy(() => CategoryPageConfigWhereInputObjectSchema)]).optional(), delete: z.union([z.boolean(),
    z.lazy(() => CategoryPageConfigWhereInputObjectSchema)]).optional(), connect: z.lazy(() => CategoryPageConfigWhereUniqueInputObjectSchema).optional().optional(), update: z.union([z.lazy(() => CategoryPageConfigUpdateToOneWithWhereWithoutCategoryInputObjectSchema), z.lazy(() => CategoryPageConfigUpdateWithoutCategoryInputObjectSchema), z.lazy(() => CategoryPageConfigUncheckedUpdateWithoutCategoryInputObjectSchema)]).optional()
}).strict() as SchemaType;
