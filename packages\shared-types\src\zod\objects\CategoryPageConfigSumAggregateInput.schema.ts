/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigSumAggregateInputType>;
export const CategoryPageConfigSumAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), categoryId: z.literal(true).optional().optional()
}).strict() as SchemaType;
