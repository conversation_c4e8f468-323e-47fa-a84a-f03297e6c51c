"use client";

import { useState } from "react";
import type { Part } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import { ModernButton } from "@/components/ui/modern-button";
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card";
import { DataTable } from "@/components/ui/data-table";
import { createColumns } from "../pro/columns";
import { HardDrive, BookType, FileText, Image as ImageIcon } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { MediaThumbnail } from "../pro/MediaThumbnail";

type PartPageIslandProps = {
  part: Part;
};

type MediaAsset = Part['mediaAssets'][0];

const MediaGallery = ({ assets }: { assets: MediaAsset[]}) => (
    <div className="grid grid-cols-4 gap-2">
        {assets.map((asset: MediaAsset) => (
            <Dialog key={asset.id}>
                <DialogTrigger asChild>
                    <button className="border rounded-lg p-2 flex flex-col items-center gap-2 hover:bg-accent transition-colors text-xs text-center">
                        {asset.mimeType.startsWith('image/') ? (
                           <>
                             <ImageIcon className="h-6 w-6 text-muted-foreground" />
                             <span>{asset.fileName}</span>
                           </>
                        ) : (
                           <>
                             <FileText className="h-6 w-6 text-muted-foreground" />
                             <span>{asset.fileName}</span>
                           </>
                        )}
                    </button>
                </DialogTrigger>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{asset.fileName}</DialogTitle>
                    </DialogHeader>
                    {asset.mimeType.startsWith('image/') ? (
                        <img src={asset.url} alt={asset.fileName} className="max-w-full h-auto rounded-lg" />
                    ) : (
                        <p>Предпросмотр для {asset.mimeType} не поддерживается. <a href={asset.url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Скачать файл</a>.</p>
                    )}
                </DialogContent>
            </Dialog>
        ))}
    </div>
)

export default function PartPageIsland({ part }: PartPageIslandProps) {

  const columns = createColumns(part.attributes);

  return (
    <div className="container mx-auto px-4 py-8">
      <ModernCard>
        <ModernCardHeader>
          <div className="flex items-start justify-between gap-4">
             <div className="flex items-start gap-6">
                <MediaThumbnail mediaAsset={part.image} size="lg" className="flex-shrink-0" />
                <div>
                  <ModernCardTitle className="text-3xl font-bold">{part.name}</ModernCardTitle>
                  <Badge variant="outline" className="mt-2">{part.partCategory?.name}</Badge>
                </div>
             </div>
            <ModernButton variant="gradient">
              <HardDrive className="mr-2 h-4 w-4" /> Запросить реализации
            </ModernButton>
          </div>
        </ModernCardHeader>
        <ModernCardContent className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-1 space-y-6">
            <div>
                <h3 className="font-bold text-lg mb-4">Эталонные характеристики</h3>
                <div className="space-y-2">
                  {part.attributes.map((attr: Part['attributes'][0]) => (
                    <div key={attr.id} className="flex items-center justify-between text-sm p-2 rounded bg-muted/50">
                      <div className="flex items-center gap-2">
                        <span className="text-muted-foreground">{attr.template.title}</span>
                        {attr.template.synonymGroups && attr.template.synonymGroups.length > 0 && (
                           <Popover>
                             <PopoverTrigger asChild>
                               <button className="text-muted-foreground hover:text-primary transition-colors">
                                 <BookType className="h-3.5 w-3.5" />
                               </button>
                             </PopoverTrigger>
                             <PopoverContent className="w-80">
                                <div className="grid gap-4">
                                    <div className="space-y-2">
                                        <h4 className="font-medium leading-none">Группы синонимов для "{attr.template.title}"</h4>
                                        <p className="text-sm text-muted-foreground">
                                            Правила эквивалентности для строковых значений.
                                        </p>
                                    </div>
                                    <div className="grid gap-2">
                                        {attr.template.synonymGroups.map((group: Part['attributes'][0]['template']['synonymGroups'][0]) => (
                                            <div key={group.id} className="text-xs p-2 rounded bg-accent/50 border">
                                                <p className="font-semibold">{group.name} <span className="text-muted-foreground font-normal">({group.compatibilityLevel})</span></p>
                                                {group.notes && <p className="text-muted-foreground italic mt-1 mb-2">"{group.notes}"</p>}
                                                <div className="flex flex-wrap gap-1 mt-1">
                                                    {group.synonyms.map((s: Part['attributes'][0]['template']['synonymGroups'][0]['synonyms'][0]) => <Badge key={s.id} variant="secondary">{s.value}</Badge>)}
                                                    {group.canonicalValue && <Badge variant="outline" title="Каноническое значение">{group.canonicalValue}</Badge>}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                             </PopoverContent>
                           </Popover>
                        )}
                      </div>
                      <div className="font-semibold text-right">
                        {attr.value} {attr.template.unit}
                        {attr.template.dataType === 'NUMBER' && attr.template.tolerance && attr.template.tolerance > 0 && (
                            <span className="text-xs text-muted-foreground ml-1">(±{attr.template.tolerance})</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
            </div>
            {part.mediaAssets.length > 0 && (
                <div>
                    <h3 className="font-bold text-lg mb-4">Медиа</h3>
                    <MediaGallery assets={part.mediaAssets} />
                </div>
            )}
          </div>
          <div className="md:col-span-2">
            <h3 className="font-bold text-lg mb-4">Каталожные позиции ({part.applicabilities.length})</h3>
            <DataTable columns={columns} data={part.applicabilities} />
          </div>
        </ModernCardContent>
      </ModernCard>
    </div>
  );
}
