import 'catalog_schema'
import 'user_schema'
import 'aggregate_schemas_schema'
import 'system_schema'



generator client {
    provider = "prisma-client-js"
}

plugin zod {
    provider = '@core/zod'
    // Генерируем Zod-схемы прямо в workspace-пакет shared-types
    output = '../packages/shared-types/src/zod'
    compile = true
    preserveTsFiles = true
}


// Дублируем генерацию Zod-схем для совместимости с серверным кодом API
plugin zod_api {
    provider = '@core/zod'
    output = 'generated/zod'
    compile = true
    preserveTsFiles = true
}

plugin trpc {
    provider = '@zenstackhq/trpc'
    output = 'generated/trpc'
    version = 'v11'
    importCreateRouter = '../../../trpc'
    importProcedure = '../../../trpc'
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}