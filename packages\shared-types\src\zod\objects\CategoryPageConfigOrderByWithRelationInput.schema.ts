/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { PartCategoryOrderByWithRelationInputObjectSchema } from './PartCategoryOrderByWithRelationInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CategoryPageConfigOrderByWithRelationInput>;
export const CategoryPageConfigOrderByWithRelationInputObjectSchema: SchemaType = z.object({
    id: z.lazy(() => SortOrderSchema).optional().optional(), categoryId: z.lazy(() => SortOrderSchema).optional().optional(), listTemplateSlug: z.lazy(() => SortOrderSchema).optional().optional(), detailTemplateSlug: z.lazy(() => SortOrderSchema).optional().optional(), listConfig: z.lazy(() => SortOrderSchema).optional().optional(), detailConfig: z.lazy(() => SortOrderSchema).optional().optional(), variables: z.lazy(() => SortOrderSchema).optional().optional(), textTemplates: z.lazy(() => SortOrderSchema).optional().optional(), seo: z.union([z.lazy(() => SortOrderSchema),
    z.lazy(() => SortOrderInputObjectSchema)]).optional(), enabled: z.lazy(() => SortOrderSchema).optional().optional(), createdAt: z.lazy(() => SortOrderSchema).optional().optional(), updatedAt: z.lazy(() => SortOrderSchema).optional().optional(), category: z.lazy(() => PartCategoryOrderByWithRelationInputObjectSchema).optional().optional()
}).strict() as SchemaType;
