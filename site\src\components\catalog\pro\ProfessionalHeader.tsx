"use client"

import { <PERSON>, Bolt, Save, Calculator, Download, <PERSON>, <PERSON><PERSON> } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernInput } from "@/components/ui/modern-input"
import { Badge } from "@/components/ui/badge"

interface ProfessionalHeaderProps {
  totalCount: number
  filteredCount: number
  searchQuery: string
  onSearchChange: (query: string) => void
  onOpenAI: () => void
}

export function ProfessionalHeader({ totalCount, filteredCount, searchQuery, onSearchChange, onOpenAI }: ProfessionalHeaderProps) {
  return (
    <header className="sticky top-14 z-40 w-full border-b border-border/40 bg-background/80 backdrop-blur-xl md:top-0">
      <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="flex bg-zinc-900 h-10 w-10 items-center justify-center rounded-xl shadow-lg">
              <Bolt className="h-5 w-5 text-primary-foreground" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold tracking-tight">Каталог</h1>
            </div>
          </div>
        </div>

        <div className="flex flex-1 items-center justify-center px-4 md:px-8">
          <div className="relative w-full max-w-lg">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <ModernInput
              placeholder="Поиск по артикулу, описанию, бренду..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 pr-4"
              variant="ghost"
            />
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Badge variant="outline" className="hidden sm:flex text-xs">
            {filteredCount} / {totalCount}
          </Badge>

          <div className="flex items-center gap-2">
            {/* <ModernButton variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-destructive"></span>
            </ModernButton> */}


            {/* <ModernButton variant="outline" size="sm">
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">Экспорт</span>
            </ModernButton> */}

            <ModernButton size="sm" onClick={onOpenAI} className="gap-2">
              <Bot className="h-4 w-4" />
              <span className="hidden sm:inline">AI Помощник</span>
            </ModernButton>
          </div>
        </div>
      </div>
    </header>
  )
}

