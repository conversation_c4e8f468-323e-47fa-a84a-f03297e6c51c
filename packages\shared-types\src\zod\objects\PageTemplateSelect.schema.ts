/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PageTemplateSelect>;
export const PageTemplateSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), area: z.boolean().optional().optional(), slug: z.boolean().optional().optional(), title: z.boolean().optional().optional(), description: z.boolean().optional().optional(), defaultConfig: z.boolean().optional().optional(), enabled: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional()
}).strict() as SchemaType;
