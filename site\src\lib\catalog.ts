// Утилиты для построения where-фильтров каталога (DRY)
// Без зависимостей от @prisma/client на фронте
export type PartWhere = Record<string, unknown>;

export function buildPartWhere(params: {
  query?: string;
  categoryId?: number;
  brandId?: number; // зарезервировано для будущих join-ов
}): PartWhere {
  const where: PartWhere = {};

  if (params.query) {
    const q = params.query.trim();
    if (q) {
      (where as any).OR = [
        { name: { contains: q, mode: 'insensitive' } },
        { partCategory: { name: { contains: q, mode: 'insensitive' } } },
      ];
    }
  }

  if (params.categoryId) (where as any).partCategoryId = params.categoryId;

  // brandId можно будет применить при добавлении join-логики (через applicabilities)
  return where;
}

// Реестр шаблонов листинга и карточки
export const listTemplates = {
  'grid-basic': 'grid-basic',
  'table-compact': 'table-compact',
} as const

export const detailTemplates = {
  'spec-two-column': 'spec-two-column',
  'tabs': 'tabs',
} as const

// Простейший рендерер текстовых шаблонов (MVP)
export function renderTemplateString(template: string, variables: Record<string, string | number | null | undefined>): string {
  if (!template) return ''
  // Обрабатываем {var?} и {var}
  return template.replace(/\{([^}]+)\}/g, (_m, expr) => {
    const optional = expr.endsWith('?')
    const key = optional ? expr.slice(0, -1) : expr
    const val = variables[key]
    if (val === undefined || val === null || val === '') {
      return optional ? '' : ''
    }
    return String(val)
  }).replace(/\s{2,}/g, ' ').trim()
}

