"use client"

import { useEffect, useState, useRef } from "react"
import { motion, useInView } from "motion/react"
import { ArrowRight, Brain, Database, Globe, Mic, MessageSquare, Sparkles, Wand2, Target, FileText, Copy, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { GridPattern } from "@/components/ui/grid-pattern"
import Ripple from "@/components/ui/ripple"
import { ManufacturersNetwork } from "@/components/landing/ManufacturersNetwork"
import { DotBackground } from "@/components/ui/dot-background"
import { TechnicalSchema } from "@/components/landing/TechnicalSchema"
import { Examples } from "@/components/landing/Examples"
import { CatalogSections } from "@/components/landing/CatalogSections"
import { PricingPlans } from "@/components/landing/PricingPlans"

function AnimatedSection({ children, className = "" }: { children: React.ReactNode; className?: string }) {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 60 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
      transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

function HowItWorksSection() {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
           <h2 className="text-5xl lg:text-5xl font-bold mb-4">
            Ключевой принцип: <span className="text-primary">Эталон и Реализация</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-12">
            В основе PartTec лежит уникальная архитектура, которая отделяет идеализированную спецификацию детали от её физических воплощений.
          </p>
        </motion.div>
        <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {/* Part Card */}
          <div className="bg-card border rounded-2xl p-8 text-left">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-12 h-12 bg-primary/10 text-primary rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6" />
              </div>
              <h3 className="text-3xl font-semibold text-card-foreground">Эталон (Part)</h3>
            </div>
            <p className="text-lg text-muted-foreground mb-4">
              Абстрактное описание детали с нормализованными, проверенными инженерами атрибутами. Это 'идеальная' спецификация.
            </p>
            <ul className="space-y-3 text-left">
                <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span><strong>Стандартизированные атрибуты:</strong> "Внутренний диаметр", "Материал".</span>
                </li>
                <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span><strong>Нормализованные значения:</strong> Все размеры в мм, все материалы по ГОСТ.</span>
                </li>
                 <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span><strong>Цель:</strong> Служить единым источником правды для сравнения.</span>
                </li>
            </ul>
          </div>
          {/* CatalogItem Card */}
          <div className="bg-card border rounded-2xl p-8 text-left">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-12 h-12 bg-chart-1/10 text-chart-1 rounded-lg flex items-center justify-center">
                <Copy className="w-6 h-6" />
              </div>
              <h3 className="text-3xl font-semibold text-card-foreground">Реализации (CatalogItem)</h3>
            </div>
            <p className="text-lg text-muted-foreground mb-4">
              Конкретные артикулы от разных производителей (SKF, Corteco), которые соответствуют эталону с определенной точностью.
            </p>
             <ul className="space-y-3 text-left">
                <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span><strong>Реальные данные:</strong> Артикул, бренд, описание от производителя.</span>
                </li>
                <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span><strong>Сопоставление с эталоном:</strong> Система определяет точность соответствия (EXACT_MATCH, MATCH_WITH_NOTES).</span>
                </li>
                 <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                    <span><strong>Цель:</strong> Показать все доступные аналоги и их отличия.</span>
                </li>
            </ul>
          </div>
        </div>
      </div>
    )
}


function AISearchDemo() {
  const [isListening, setIsListening] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [aiResponse, setAiResponse] = useState("")
  const [step, setStep] = useState(0)

  const demoSteps = [
    {
      query: "Нужен сальник для гидроцилиндра Caterpillar 320D",
      response:
        "Анализирую запрос... Найдено 15 совместимых сальников для гидроцилиндра Caterpillar 320D. Показываю варианты с экономией до 45%.",
    },
    {
      query: "Масляный фильтр для двигателя Cummins ISX15",
      response: "Обрабатываю... Найдено 8 аналогов масляного фильтра. Все варианты в наличии, совместимость 98-100%.",
    },
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      const currentStep = demoSteps[step]
      setSearchQuery(currentStep.query)
      setTimeout(() => {
        setAiResponse(currentStep.response)
      }, 1500)
      setTimeout(() => {
        setStep((prev) => (prev + 1) % demoSteps.length)
        setSearchQuery("")
        setAiResponse("")
      }, 4000)
    }, 6000)

    return () => clearInterval(interval)
  }, [step])

  return (
    <div className="relative">
      <div className="absolute inset-0 bg-primary/5 rounded-2xl" />
      <Ripple />

      <div className="relative bg-card backdrop-blur-xl border rounded-2xl p-6 shadow-2xl">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* AI Search Interface */}
          <div className="space-y-3">
            <div className="flex items-center gap-3 mb-3">
              <div className="relative">
                <div className="w-12 h-12 bg-secondary rounded-xl flex items-center justify-center">
                  <Brain className="w-6 h-6 text-secondary-foreground" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse" />
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-card-foreground">ИИ-Ассистент на базе эталонов</h3>
                <p className="text-base text-muted-foreground">Умный поиск, построенный на структурированных данных</p>
              </div>
            </div>

            <div className="relative">
              <div className="absolute -inset-0.5 bg-primary rounded-lg blur opacity-30" />
              <div className="relative bg-card backdrop-blur-xl border rounded-lg p-4">
                <div className="flex items-center gap-3 mb-4">
                  <Database className="w-5 h-5 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Опишите нужную запчасть или технику..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 bg-transparent text-foreground placeholder:text-muted-foreground focus:outline-none"
                  />
                  <motion.button
                    onClick={() => setIsListening(!isListening)}
                    className={`p-2 rounded-lg transition-all ${
                      isListening
                        ? "bg-red-500 text-white animate-pulse"
                        : "bg-secondary text-secondary-foreground hover:bg-accent"
                    }`}
                  >
                    <Mic className="w-4 h-4" />
                  </motion.button>
                </div>

                {aiResponse && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-secondary border rounded-lg p-4"
                  >
                    <div className="flex items-start gap-3">
                      <MessageSquare className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <div className="text-base text-secondary-foreground">{aiResponse}</div>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </div>

          {/* AI Features */}
          <div className="space-y-3">
            <h4 className="text-2xl font-semibold text-card-foreground flex items-center gap-2">
              <Wand2 className="w-5 h-5 text-primary" />
              Возможности поиска
            </h4>

            <div className="space-y-4">
              {[
                {
                  icon: Brain,
                  title: "Понимание естественного языка",
                  description: "ИИ анализирует ваш запрос и техническую документацию, чтобы понять контекст.",
                  color: "bg-secondary",
                },
                {
                  icon: FileText,
                  title: "Сопоставление по эталонам",
                  description: "Подбор аналогов происходит на основе сравнения с нормализованными атрибутами эталона (Part).",
                  color: "bg-secondary",
                },
                {
                  icon: Target,
                  title: "Точная фильтрация",
                  description: "Автоматический подбор по десяткам технических характеристик с учетом допусков.",
                  color: "bg-secondary",
                },
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start gap-4 p-4 bg-card backdrop-blur-xl border rounded-lg transition-all duration-300"
                >
                  <div
                    className={`w-10 h-10 ${feature.color} rounded-lg flex items-center justify-center flex-shrink-0`}
                  >
                    <feature.icon className="w-5 h-5 text-secondary-foreground" />
                  </div>
                  <div>
                    <h5 className="font-medium text-card-foreground mb-1">{feature.title}</h5>
                    <p className="text-base text-muted-foreground">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function LandingPage() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <div className="min-h-screen text-foreground overflow-hidden">
      {/* Hero Section */}
      <DotBackground className="min-h-screen">
        <section className="relative lex items-center justify-center">

          <motion.div className="relative container mx-auto px-4 py-5 text-center z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, ease: [0.25, 0.46, 0.45, 0.94] }}
            >

              <h1 className="text-6xl lg:text-7xl font-bold mb-4 leading-tight text-foreground">
                Инженерная система <span className="text-primary">эталонных</span> запчастей и их аналогов
              </h1>

              <div className="space-y-8 max-w-5xl mx-auto">
                {/* Основное описание */}
                <div className="text-center">
                  <p className="text-xl text-muted-foreground leading-relaxed mb-2">
                    <span className="text-foreground font-semibold text-2xl">PartTec</span> — это больше, чем каталог
                  </p>
                  <p className="text-base text-muted-foreground max-w-2xl mx-auto">
                    Инновационная система точного подбора запчастей и аналогов
                  </p>
                </div>

                {/* Ключевые преимущества */}
                <div className="grid md:grid-cols-2 gap-8">
                  <div className="flex flex-col items-start space-y-4 p-6 rounded-xl bg-gradient-to-br from-primary/5 to-transparent border border-primary/10">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-primary font-bold text-lg">1</span>
                      </div>
                      <h3 className="text-xl font-semibold text-foreground">
                        Цифровые эталоны
                      </h3>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      Создаем точные цифровые модели каждой детали на основе её <span className="text-foreground font-medium">физических характеристик</span> и технических параметров
                    </p>
                  </div>

                  <div className="flex flex-col items-start space-y-4 p-6 rounded-xl bg-gradient-to-br from-primary/5 to-transparent border border-primary/10">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-primary font-bold text-lg">2</span>
                      </div>
                      <h3 className="text-xl font-semibold text-foreground">
                        Прямая связь
                      </h3>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      Вместо неточных кросс-номеров — <span className="text-foreground font-medium">напрямую связываем</span> товары производителей с эталонными моделями
                    </p>
                  </div>
                </div>

                {/* Результат */}
                <div className="text-center">
                  <div className="inline-flex items-center gap-4 bg-primary/10 rounded-2xl p-8 border-2 border-primary/20">
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">✓</span>
                    </div>
                    <div className="text-left">
                      <h3 className="text-2xl font-bold text-foreground mb-2">Результат</h3>
                      <p className="text-lg text-muted-foreground">
                        <span className="text-primary font-semibold">Беспрецедентная точность</span> подбора аналогов на основе реальных данных, а не таблиц соответствия
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex flex-col sm:flex-row gap-4 justify-center mt-16"
              >
                <a href="/register" className=" no-underline group cursor-pointer relative shadow-2xl shadow-zinc-900 rounded-full p-px text-lg font-semibold leading-6 text-white inline-block">
                  <span className="absolute inset-0 overflow-hidden rounded-full">
                    <span className="absolute inset-0 rounded-full bg-[image:radial-gradient(75%_100%_at_50%_0%,rgba(56,189,248,0.6)_0%,rgba(56,189,248,0)_75%)] opacity-0 transition-opacity duration-500 group-hover:opacity-100" />
                  </span>
                  <div className="relative flex space-x-2 items-center z-10 rounded-full bg-zinc-950 py-4 px-8 ring-1 ring-white/10">
                    <span>Получить доступ</span>
                    <ArrowRight className="w-5 h-5" />
                  </div>
                </a>
              </motion.div>
            </motion.div>
          </motion.div>
        </section>
      </DotBackground>

      {/* How It Works Section */}
        <AnimatedSection className="py-16 relative">
            <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary to-background" />
            <GridPattern className="opacity-20" />
            <div className="relative">
                <HowItWorksSection />
            </div>
        </AnimatedSection>


      {/* AISearchDemo Section */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary to-background" />
        <GridPattern className="opacity-20" />
        <div className="relative container mx-auto px-4">
          <AISearchDemo />
        </div>
      </AnimatedSection>

      {/* TechnicalSchema Section */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary to-background" />
        <GridPattern className="opacity-20" />
        <div className="relative container mx-auto px-4">
          <TechnicalSchema />
        </div>
      </AnimatedSection>

      {/* Examples Section */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary to-background" />
        <GridPattern className="opacity-20" />
        <Examples />
      </AnimatedSection>

      {/* Catalog Sections */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary to-background" />
        <GridPattern className="opacity-20" />
        <CatalogSections />
      </AnimatedSection>


      {/* Manufacturers Network Section */}
      <AnimatedSection className="py-20 relative mt-16">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary to-background" />
        <GridPattern className="opacity-20" />
        <div className="relative container mx-auto px-4">
          <div className="text-center mb-8">

      {/* Pricing Section */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary to-background" />
        <GridPattern className="opacity-20" />
        <div className="relative container mx-auto px-4">
          <PricingPlans />
        </div>
      </AnimatedSection>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 bg-secondary border text-primary px-4 py-2 rounded-full text-base font-medium mb-3"
            >
              <Globe className="w-4 h-4" />
              Сеть производителей
            </motion.div>
            <h2 className="text-5xl lg:text-5xl font-bold mb-3">
              <span className="text-muted-foreground">Единая экосистема</span>{" "}
              <span className="text-primary">производителей</span>
            </h2>
            <p className="text-2xl text-muted-foreground max-w-3xl mx-auto">
              Наш каталог объединяет ведущих мировых производителей в единую сеть взаимозаменяемых запчастей
            </p>
          </div>

          <ManufacturersNetwork />
        </div>
      </AnimatedSection>

      {/* CTA bottom */}
      <AnimatedSection className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h3 className="text-4xl font-bold mb-4">Начните с бесплатной регистрации</h3>
          <p className="text-lg text-muted-foreground mb-6">Получите доступ к профессиональному каталогу и инструментам</p>
          <a href="/register" className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-10 px-6">
            Зарегистрироваться
          </a>
        </div>
      </AnimatedSection>
    </div>
  )
}

