---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/clients";
import PartPageIsland from "@/components/catalog/islands/PartPageIsland.tsx";
import type { Part } from "@/lib/types";

const { id } = Astro.params;

if (!id || isNaN(Number(id))) {
  return Astro.redirect('/catalog');
}

let part: Part | null = null;

try {
  // prettier-ignore
  part = await trpcClient.crud.part.findUnique.query({
    where: { id: Number(id) },
    include: {
      partCategory: true,
      image: true,
      mediaAssets: true,
      attributes: {
        include: {
          template: {
            include: {
              synonymGroups: {
                include: {
                  synonyms: true
                }
              }
            }
          },
        },
      },
      applicabilities: {
        include: {
          catalogItem: {
            include: {
              brand: true,
              image: true,
              mediaAssets: true,
              attributes: {
                include: {
                  template: true,
                },
              },
            },
          },
        },
      },
    },
  }) as Part;
} catch (error) {
  console.error('Error preloading part data:', error);
}

if (!part) {
  return Astro.redirect('/catalog');
}
---

<MainLayout title={part.name || `Запчасть #${part.id}`} description={`Группа взаимозаменяемости`}>
  <PartPageIsland client:load part={part} />
</MainLayout>


